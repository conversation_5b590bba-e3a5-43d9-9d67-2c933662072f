name: Release CSGHub Docker Compose

on:
  push:
    tags:
      - 'v\d+\.\d+\.\d+'

jobs:
  assets:
    permissions:
      contents: write
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          lfs: true

      - name: Package Custom Artifact
        id: package_artifact
        run: |
          docker_artifact="csghub-docker-compose-${{ github.ref_name }}.tgz"
          chart_artifact="csghub-helm-chart-${{ github.ref_name }}.tgz"
          mv docker/compose csghub && tar -zcf $docker_artifact csghub
          tar -zcf $chart_artifact helm/charts/csghub
          echo "artifacts=$docker_artifact,$chart_artifact" >> $GITHUB_OUTPUT

      - name: Check if Release Exists
        id: check_if_exists
        run: |
          response=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" "https://api.github.com/repos/${{ github.repository }}/releases/tags/${{ github.ref_name }}")
          if echo "$response" | grep -q "id"; then
            upload_url=$(echo "$response" | jq -r '.upload_url')
            echo "release_exists=true" >> $GITHUB_ENV
            echo "upload_url=$upload_url" >> $GITHUB_OUTPUT
          else
            echo "release_exists=false" >> $GITHUB_ENV
          fi

      - name: Get Previous Tag
        id: get_previous_tag
        run: |
          ALL_TAGS=$(git tag --sort=-v:refname)
          CURRENT_TAG=${GITHUB_REF#refs/tags/}
          PREVIOUS_TAG=$(echo "$ALL_TAGS" | grep -F -x -A 1 "$CURRENT_TAG" | tail -n 1)
          echo ${PREVIOUS_TAG}..${CURRENT_TAG}
          echo "previous_tag=$PREVIOUS_TAG" >> $GITHUB_ENV

      - name: Generate Release Notes
        id: generate_release_note
        if: env.release_exists == 'false'
        run: |
          response=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" "https://api.github.com/repos/${{ github.repository }}/releases/generate-notes" \
            -d '{"tag_name":"${{ github.ref_name }}","target_commitish":"${{ github.sha }}","previous_tag_name":"${{ env.previous_tag }}"}')
          if [[ $? -eq 0 ]] && [[ ! -z "$response" ]]; then
            body=$(echo "$response" | jq -r '.body')
            echo "$body" > release-notes.md
            cat release-notes.md
          fi

      - name: Upload Release Assets
        if: env.release_exists == 'true'
        uses: softprops/action-gh-release@v2
        with:
          files: |
            ${{ steps.package_artifact.outputs.artifacts }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish Release
        uses: ncipollo/release-action@v1
        if: env.release_exists == 'false'
        with:
          artifacts: ${{ steps.package_artifact.outputs.artifacts }}
          bodyFile: "release-notes.md"