name: Release CSGHub Helm Chart

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+-ce'

jobs:
  package:
    permissions:
      contents: write
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true

      - name: Configure Git
        run: |
          git config user.name "$GITHUB_ACTOR"
          git config user.email "$<EMAIL>"

      - name: Install <PERSON><PERSON>
        uses: azure/setup-helm@v4

      - name: Run chart-releaser
        uses: helm/chart-releaser-action@v1.6.0
        with:
          charts_dir: helm/charts
        env:
          CR_TOKEN: "${{ secrets.GITHUB_TOKEN }}"