{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations: {{ .Values.annotations | toYaml | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      annotations:
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      initContainers:
        - name: wait-for-redis
          image: {{ or .Values.global.image.registry .Values.image.registry }}/redis:7.2.5
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until redis-cli -h {{ include "csghub.redis.host" . }} -p {{ include "csghub.redis.port" . }} ping; do echo 'Wait for Redis to be ready'; sleep 2; done" ]
          envFrom:
            {{- if .Values.global.redis.external }}
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
            {{- else }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "redis") }}
            {{- end }}
          env:
            - name: REDISCLI_AUTH
              {{- if .Values.global.redis.external }}
              value: "$(STARHUB_SERVER_REDIS_PASSWORD)"
              {{- else }}
              value: "$(REDIS_PASSWD)"
              {{- end }}
        - name: wait-for-postgresql
          image:  {{ or .Values.global.image.registry .Values.image.registry }}/opencsg/psql:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until pg_isready; do echo 'Wait for PostgreSQL to be ready'; sleep 2; done" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
          env:
            - name: PGHOST
              value: "$(STARHUB_DATABASE_HOST)"
            - name: PGPORT
              value: "$(STARHUB_DATABASE_PORT)"
            - name: PGDATABASE
              value: "$(STARHUB_DATABASE_NAME)"
            - name: PGUSER
              value: "$(STARHUB_DATABASE_USERNAME)"
            - name: PGPASSWORD
              value: "$(STARHUB_DATABASE_PASSWORD)"
        - name: wait-for-server
          image: {{ or .Values.global.image.registry .Values.image.registry }}/busybox:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until nc -z {{ include "server.internal.domain" . }} {{ include "server.internal.port" . }}; do echo 'Wait for csghub-server to be ready'; sleep 2; done" ]
      containers:
        - name: mirror-repo
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ or .Values.global.image.name .Values.image.repository }}:{{ include "csghub.image.tag" (dict "tag" (or .Values.global.image.tag .Values.image.tag) "context" .) }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub mirror repo-sync" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
            {{- if not .Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "redis") }}
            {{- end }}
            {{- if not .Values.global.objectStore.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "minio") }}
            {{- end }}
          env:
            {{- if not .Values.global.redis.external }}
            - name: STARHUB_SERVER_REDIS_PASSWORD
              value: "$(REDIS_PASSWD)"
            {{- end }}
            {{- if not .Values.global.objectStore.external }}
            - name: STARHUB_SERVER_S3_ACCESS_KEY_ID
              value: "$(MINIO_ROOT_USER)"
            - name: STARHUB_SERVER_S3_ACCESS_KEY_SECRET
              value: "$(MINIO_ROOT_PASSWORD)"
            {{- end }}
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
        - name: mirror-lfs
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ or .Values.global.image.name .Values.image.repository }}:{{ include "csghub.image.tag" (dict "tag" (or .Values.global.image.tag .Values.image.tag) "context" .) }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub mirror lfs-sync" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
            {{- if not .Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "redis") }}
            {{- end }}
            {{- if not .Values.global.objectStore.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "minio") }}
            {{- end }}
          env:
            {{- if not .Values.global.redis.external }}
            - name: STARHUB_SERVER_REDIS_PASSWORD
              value: "$(REDIS_PASSWD)"
            {{- end }}
            {{- if not .Values.global.objectStore.external }}
            - name: STARHUB_SERVER_S3_ACCESS_KEY_ID
              value: "$(MINIO_ROOT_USER)"
            - name: STARHUB_SERVER_S3_ACCESS_KEY_SECRET
              value: "$(MINIO_ROOT_PASSWORD)"
            {{- end }}
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}