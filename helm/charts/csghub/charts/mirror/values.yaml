## Default values for CSGHub-Mirror.
## This is a YAML-formatted file.
## Declare variables to be passed into your templates.

## Define deploy replicas
replicas: 1

## Configuration for images, it can be overwritten by global.images
image:
  ## List of image pull secrets.
  ## Used to pull Docker images from private repositories.
  ## This array is empty by default, meaning no secrets are required by default.
  pullSecrets: []
  ## Specify path prefix relative to docker.io
  ## eg: minio/minio:latest with prefix {{ prefix }}/minio/minio:latest
  ## No need to add the final slash `/`
  registry: "opencsg-registry.cn-beijing.cr.aliyuncs.com/opencsg_public"
  ## Specifies the location of the Registry Docker image in the registry.
  repository: "csghub_server"
  ## Defines the specific version of the Registry image to use.
  ## The tag will be automatically suffixed with edition (ce/ee) based on global.edition
  tag: "v1.8.0"
  ## Determines how the image should be pulled from the registry.
  pullPolicy: "IfNotPresent"

## Specifies the location and credentials for accessing the external PostgreSQL database.
postgresql:
  ## Specifies the host address of the PostgreSQL database server.
  host: "csghub-postgresql"
  ## Defines the port on which the PostgreSQL server is listening.
  port: 5432
  ## The username used for authentication with the PostgreSQL database.
  user: "csghub_server"
  ## The password used for authentication with the PostgreSQL database.
  ## It is empty by default for security reasons and should be set through secure means.
  password: ""
  ## The name of the database to connect to on the PostgreSQL server.
  database: "csghub_server"
  ## Sets the timezone for the database connection, ensuring time-based operations use the correct timezone.
  timezone: "Etc/UTC"

## Specifies the location and credentials for accessing the external Redis cache.
redis:
  ## host for accessing Redis
  host: ""
  ## Port for accessing Redis
  port: 6379
  ## Password for accessing Redis
  password: ""

## Specifies the location and credentials for accessing the external Object Storage (OSS) service.
objectStore:
  ## The endpoint URL of the object storage service.
  endpoint: "http://minio.<domain>"
  ## The access key ID for authentication with the object storage service.
  ## This is empty by default and should be provided securely.
  accessKey: "minio"
  ## The secret access key for authentication with the object storage service.
  ## This is also empty by default and should be provided securely.
  accessSecret: ""
  ## The name of the bucket within the object storage service to be used.
  bucket: "csghub-server"
  ## The region where the bucket is located within the object storage service.
  region: "cn-north-1"
  ## If encrypted with TLS
  encrypt: false
  ## When set to "true", the bucket name will be part of the URL path.
  ## For example, the URL will be in the format:
  ## http://<minio-server>/<bucket-name>/<object-key>
  pathStyle: "true"

serviceAccount:
  ## Determines whether a service account should be created.
  create: false
  ## Controls whether the service account token should be automatically mounted.
  automount: false
  ## Allows for annotations to be added to the service account.
  annotations: {}

## podAnnotations: Allows you to add annotations to the pods. Annotations can be used to attach arbitrary -
## non-identifying metadata to objects. Tools and libraries can retrieve this metadata.
podAnnotations: {}

## podLabels: Provides the ability to add labels to the pods. Labels are key/value pairs that are attached to objects, -
## such as pods, which can be used for the purposes of organization and to select subsets of objects.
podLabels: {}

## podSecurityContext: Defines security settings for the entire pod. This can include settings like the user and group -
## IDs that processes run as, and privilege and access control settings.
podSecurityContext: {}

## securityContext: Specifies security settings for a specific container within a pod. This can include settings such as -
## capabilities, security enhanced Linux (SELinux) options, and whether the container should run as privileged.
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

## environments: This section is reserved for defining environment variables for the Nats container.
## Environment variables can be used to customize the behavior of the Nats instance.
## For example, you might use environment variables to configure logging levels or to enable certain Nats features.
## This section is currently empty, indicating that no environment variables have been explicitly set.
environments: {}

## annotations: This section allows you to add annotations to the Nats deployment.
## Annotations are key-value pairs that can be used to store additional metadata about the deployment.
## This can be useful for tools and applications that interact with your Kubernetes cluster, providing them with extra -
## information about your Nats instance.
## Like the environments section, this is also currently empty.
annotations: {}
#  helm.sh/resource-policy: keep

## The 'resources' section is used to define the compute resource requirements for the Nats container.
## Here, you can specify the minimum and maximum amount of CPU and memory that the container is allowed to use.
## Leaving this section empty means that no specific resource limits or requests are set for the Nats container.
## This approach can be beneficial in environments with limited resources, such as development or testing environments,
## where you might not want to enforce strict resource constraints.
## However, for production environments, it's recommended to uncomment and set these values to ensure that the Nats container
## has enough resources to operate efficiently and to prevent it from consuming too much of the available resources on the node.
## 'limits' specify the maximum amount of CPU and memory the container can use.
## 'requests' specify the minimum amount of CPU and memory guaranteed to the container.
## If these values are not set, the container could be terminated in a resource-constrained environment or it might not perform as expected.
resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

## nodeSelector: This section allows you to specify node labels for pod assignment.
## This is useful for ensuring that pods are only scheduled on nodes with specific labels.
nodeSelector: {}

## tolerations: This section allows you to specify tolerations for the pods.
## Tolerations enable the pods to schedule onto nodes with matching taints.
## This is useful in scenarios where you want to ensure that certain workloads run on dedicated nodes.
tolerations: []

## affinity: This section allows you to set rules that affect how pods are scheduled based on various criteria -
## including labels of pods that are already running on the node.
## Affinity settings can be used to ensure that certain pods are co-located in the same node, zone, etc., or to -
## spread pods across nodes or zones for high availability.
affinity: {}

## autoscaling: This section configures the Horizontal Pod Autoscaler (HPA) for the Nats deployment.
## The HPA automatically scales the number of pods in a deployment, replication controller, replica set, or stateful -
## set based on observed CPU utilization.
autoscaling:
  ## Determines whether autoscaling is enabled. Set to true to enable autoscaling.
  enabled: false
  ## The minimum number of replicas. The autoscaler will not scale below this number.
  minReplicas: 1
  ## The maximum number of replicas. The autoscaler will not scale above this number.
  maxReplicas: 100
  ## The target average CPU utilization (represented as a percentage) over all the pods. When the average CPU utilization -
  ## exceeds this threshold, the HPA will scale up.
  targetCPUUtilizationPercentage: 80
  ## Uncomment to enable scaling based on memory usage. This sets the target average memory utilization over all the pods.
  # targetMemoryUtilizationPercentage: 80
