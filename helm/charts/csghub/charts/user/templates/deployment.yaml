{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations: {{ .Values.annotations | toYaml | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      annotations:
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      initContainers:
        - name: wait-for-postgresql
          image:  {{ or .Values.global.image.registry .Values.image.registry }}/opencsg/psql:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until pg_isready; do echo 'Wait for PostgreSQL to be ready'; sleep 2; done" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
          env:
            - name: PGHOST
              value: "$(STARHUB_DATABASE_HOST)"
            - name: PGPORT
              value: "$(STARHUB_DATABASE_PORT)"
            - name: PGDATABASE
              value: "$(STARHUB_DATABASE_NAME)"
            - name: PGUSER
              value: "$(STARHUB_DATABASE_USERNAME)"
            - name: PGPASSWORD
              value: "$(STARHUB_DATABASE_PASSWORD)"
        - name: wait-for-server
          image: {{ or .Values.global.image.registry .Values.image.registry }}/busybox:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until nc -z {{ include "server.internal.domain" . }} {{ include "server.internal.port" . }}; do echo 'Wait for csghub-server to be ready'; sleep 2; done" ]
      containers:
        - name: user
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ or .Values.global.image.name .Values.image.repository }}:{{ include "csghub.image.tag" (dict "tag" (or .Values.global.image.tag .Values.image.tag) "context" .) }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub user launch" ]
          ports:
            - containerPort: 8080
              name: user
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ include  "common.names.custom" (list . "casdoor") }}
            - configMapRef:
                name: {{ include  "common.names.custom" (list . "server") }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "nats") }}
          env:
            - name: OPENCSG_USER_SERVER_PORT
              value: "8080"
            - name: OPENCSG_USER_SERVER_SIGNIN_SUCCESS_REDIRECT_URL
              value: {{ include "server.callback.user" . }}
            - name: OPENCSG_ACCOUNTING_NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" . }}:{{ include "nats.internal.ports.api" . }}"
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 20
            periodSeconds: 10
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: jwt-token-crt
              mountPath: /starhub-bin/casdoor
      volumes:
        - name: jwt-token-crt
          secret:
            secretName: {{ include "common.names.custom" (list . "casdoor") }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}