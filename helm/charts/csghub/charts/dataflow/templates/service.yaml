{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.global.dataflow.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
spec:
  clusterIP: None
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 8000
      protocol: TCP
      name: dataflow
  selector:
    {{- include "common.labels.selector" . | nindent 4 }}
{{- end }}