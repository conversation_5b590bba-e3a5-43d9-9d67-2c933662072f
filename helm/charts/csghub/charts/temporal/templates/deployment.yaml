{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.enabled }}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  {{- with (include "common.annotations.deployment" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      {{- with (include "common.annotations.pod.checksum" (dict "context" . "configmap" false "secret" false)) }}
      annotations:
        {{- . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      initContainers:
        - name: wait-for-postgresql
          image: {{ or .Values.global.image.registry .Values.image.registry }}/opencsg/psql:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until pg_isready; do echo 'Wait for PostgreSQL to be ready'; sleep 2; done" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" . }}
          env:
            - name: PGHOST
              value: "$(POSTGRES_SEEDS)"
            - name: PGPORT
              value: "$(DB_PORT)"
            - name: PGDATABASE
              value: "$(DBNAME)"
            - name: PGUSER
              value: "$(POSTGRES_USER)"
            - name: PGPASSWORD
              value: "$(POSTGRES_PWD)"
      containers:
        - name: temporal
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 7233
              name: temporal
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" . }}
            {{- if not .Values.global.postgresql.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "postgresql") }}
            {{- end }}
          {{- with .Values.environments }}
          env:
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          lifecycle:
            postStart:
              exec:
                command: [ "/bin/sh", "-c", "sleep 8" ]
          livenessProbe:
            tcpSocket:
              port: 7233
            initialDelaySeconds: 20
            periodSeconds: 10
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
        {{- if .Values.ingress.enabled }}
        - name: temporal-ui
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.ui.repository }}:{{ .Values.image.ui.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 8080
              name: temporal-ui
              protocol: TCP
          env:
            - name: TEMPORAL_ADDRESS
              value: {{ include "temporal.internal.endpoint" . }}
            - name: TEMPORAL_CORS_ORIGINS
              value: "http://localhost:3000"
            - name: TEMPORAL_CSRF_COOKIE_INSECURE
              value: "true"
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 20
            periodSeconds: 10
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}