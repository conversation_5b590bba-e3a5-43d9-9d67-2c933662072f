{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.enabled .Values.ingress.enabled }}
apiVersion: {{ include "common.capabilities.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    {{- include "common.annotations.ingress.nginx" (dict "auth" (dict "secret" (include "common.names.custom" .) "realm" "Authentication Required - temporal") "custom" .Values.ingress.annotations) | nindent 4 }}
spec:
  ingressClassName: {{ .Values.global.ingress.className | default "nginx" }}
  {{- if eq (include "global.ingress.tls.enabled" .) "true" }}
  tls:
    - hosts:
        - {{ include "temporal.external.domain" . }}
      {{- $secret := coalesce (include "global.ingress.tls.secret" .) .Values.ingress.tls.secretName }}
      {{- if $secret }}
      secretName: {{ $secret }}
      {{- else }}
      {{ fail "Temporal-UI ingress TLS is enabled but no secretName is provided." }}
      {{- end }}
  {{- end }}
  rules:
    - host: {{ include "temporal.external.domain" . }}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              {{- if eq (include "common.capabilities.ingress.apiVersion" .) "networking.k8s.io/v1" }}
              service:
                name: {{ include "common.names.custom" . }}
                port:
                  number: 8080
              {{- else }}
              serviceName: {{ include "common.names.custom" . }}
              servicePort: 8080
              {{- end }}
{{- end }}