{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations: {{ .Values.annotations | toYaml | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  serviceName: {{ include "common.names.custom" . }}
  replicas: 1
  minReadySeconds: 30
  template:
    metadata:
      annotations:
        checksum/config-secret: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      containers:
        - name: nats
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 4222
              name: api
              protocol: TCP
            - containerPort: 6222
              name: cluster
              protocol: TCP
            - containerPort: 8222
              name: monitor
              protocol: TCP
          envFrom:
            - secretRef:
                name: {{ include "common.names.custom" . }}
          {{- with .Values.environments }}
          env:
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 4222
            initialDelaySeconds: 10
            periodSeconds: 5
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /data/jetstream
            - name: nats-config
              mountPath: /nats-server.conf
              subPath: nats-server.conf
      volumes:
        - name: nats-config
          configMap:
            name: {{ include "common.names.custom" . }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: data
        namespace: {{ .Release.Namespace }}
        labels:
          {{- include "common.labels" . | nindent 10 }}
        annotations:
          helm.sh/resource-policy: keep
          {{- with .Values.annotations }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
      spec:
        accessModes: {{ or .Values.global.persistence.accessMode .Values.persistence.accessMode }}
        {{- if .Values.persistence.storageClass }}
        storageClassName: {{ or .Values.global.persistence.storageClass .Values.persistence.storageClass }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size }}