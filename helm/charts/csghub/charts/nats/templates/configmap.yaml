{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- $secretData := (lookup "v1" "Secret" .Release.Namespace (include "common.names.custom" .)).data -}}
{{- $username := "natsadmin" -}}
{{- $password := randAlphaNum 15 -}}
{{- $htpasswd := htpasswd $username $password -}}
{{- if $secretData }}
{{- $secretUsername := index $secretData "NATS_USERNAME" }}
{{- if $secretUsername }}
{{- $username = $secretUsername | b64dec }}
{{- end }}
{{- $secretPassword := index $secretData "NATS_PASSWORD" }}
{{- if $secretPassword }}
{{- $password = $secretPassword | b64dec }}
{{- end }}
{{- $secretHtpasswd := index $secretData "HTPASSWD" }}
{{- if $secretHtpasswd }}
{{- $htpasswd = $secretHtpasswd | b64dec }}
{{- else }}
{{- $htpasswd = htpasswd $username $password -}}
{{- end }}
{{- end -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    helm.sh/resource-policy: keep
    resource.dependencies/deployments: |
      {{ include "common.names.custom" (list . "server") }}
      {{ include "common.names.custom" (list . "user") }}
      {{ include "common.names.custom" (list . "portal") }}
      {{ include "common.names.custom" (list . "accounting") }}
      {{ include "common.names.custom" (list . "runner") }}
      {{ include "common.names.custom" (list . "proxy") }}
    resource.dependencies/statefulsets: |
      {{ include "common.names.custom" . }}
type: Opaque
data:
  NATS_USERNAME: {{ $username | b64enc }}
  NATS_PASSWORD: {{ $password | b64enc }}
  HTPASSWD: {{ $htpasswd | b64enc }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  nats-server.conf: |
    debug = false
    trace = false

    # Client port of 4222 on all interfaces
    port: 4222

    # HTTP monitoring port
    monitor_port: 8222

    jetstream: enabled

    jetstream {
      store_dir: /data/jetstream
      max_mem: 8G
      max_file: 10G
    }

    authorization {
      ADMIN = {
        publish = ">"
        subscribe = ">"
    }

    users = [
        { user: {{ $username | quote }}, password: {{ $htpasswd | trimPrefix (printf "%s:" $username) | quote }}, permissions: $ADMIN }
      ]
    }