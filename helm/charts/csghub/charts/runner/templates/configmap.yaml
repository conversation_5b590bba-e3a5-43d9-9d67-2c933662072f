{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.global.deploy.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  # Specify the namespace to use and select it based on the environment
  {{- if .Values.global.deploy.mergingNamespace }}
  STARHUB_SERVER_RUNNER_IMAGE_BUILDER_NAMESPACE: {{ .Values.global.deploy.namespace }}
  {{- else }}
  STARHUB_SERVER_RUNNER_IMAGE_BUILDER_NAMESPACE: {{ .Values.global.deploy.imageBuilder.namespace }}
  {{- end }}
  # The image used by the pod needs to be accessible in the pod
  STARHUB_SERVER_RUNNER_IMAGE_BUILDER_GIT_IMAGE: {{ or .Values.global.image.registry .Values.image.registry }}/alpine/git:2.36.2
  STARHUB_SERVER_RUNNER_IMAGE_BUILDER_KANIKO_IMAGE: {{ or .Values.global.image.registry .Values.image.registry }}/kaniko-project-executor:v1.23.2
  # The cleanup time after the image builder task is completed, the default is 2 minutes
  STARHUB_SERVER_RUNNER_IMAGE_BUILDER_JOB_TTL: "300"
  # Task status correction time, default is 5 minutes
  STARHUB_SERVER_RUNNER_IMAGE_BUILDER_STATUS_TTL: "300"
  {{- if or (not .Values.global.registry.external) .Values.global.registry.insecure }}
  STARHUB_SERVER_RUNNER_IMAGE_BUILDER_KANIKO_ARGS: "--skip-tls-verify,--insecure,--compressed-caching=false,--single-snapshot,--build-arg=PyPI={{ .Values.pipIndexUrl }},--build-arg=HF_ENDPOINT={{ printf "%s/hf" (include "csghub.external.endpoint" .) }}"
  {{- else }}
  STARHUB_SERVER_RUNNER_IMAGE_BUILDER_KANIKO_ARGS: "--compressed-caching=false,--single-snapshot,--build-arg=PyPI={{ .Values.global.deploy.pipIndexUrl }},--build-arg=HF_ENDPOINT={{ printf "%s/hf" (include "csghub.external.endpoint" .) }}"
  {{- end }}
  {{- if .Values.global.deploy.autoConfigure }}
  configure.sh: |
    #!/bin/bash

    # Argo Workflows installation function
    argo_install() {
      echo "Starting Argo Workflows installation..."
      kubectl apply -f /argo/argo.yaml
      kubectl apply -f /argo/rbac.yaml
      echo "Argo Workflows installation completed."
      touch /tmp/skip_argo_install
    }

    # Knative Serving installation function
    knative_install() {
      echo "Starting Knative Serving installation..."
      kubectl apply -f /knative/serving-crds.yaml
      kubectl apply -f /knative/serving-core.yaml
      kubectl apply -f /knative/kourier.yaml
      kubectl apply -f /knative/serving-hpa.yaml
      echo "Knative Serving installation completed."
      touch /tmp/skip_knative_install
    }

    # Argo Workflows installation function
    lws_install() {
      echo "Starting LeaderWorkSet installation..."
      kubectl apply -f /lws/manifests.yaml
      echo "LeaderWorkSet installation completed."
      touch /tmp/skip_lws_install
    }

    # Space Application secret create
    space_install() {
      echo "Starting Space Application pre-install..."
      kubectl apply -f /space/space-app.yaml
      echo ""
    }

    # Function to check namespace, deployment, and service
    service_verify() {
      local namespace="${1:-argo}"
      local deployment="${2:-argo-server}"
      local service="${3:-argo-server}"

      if ! kubectl get namespace "$namespace" &>/dev/null; then
        echo "Namespace '$namespace' does not exist."
        return 1
      fi

      if ! kubectl get deployment "$deployment" -n "$namespace" &>/dev/null; then
        echo "Deployment '$deployment' does not exist in namespace '$namespace'."
        return 1
      fi

      local available_replicas total_replicas
      available_replicas=$(kubectl get deployment "$deployment" -n "$namespace" -o=jsonpath='{.status.availableReplicas}' 2>/dev/null)
      total_replicas=$(kubectl get deployment "$deployment" -n "$namespace" -o=jsonpath='{.status.replicas}' 2>/dev/null)
      if [[ -z "$available_replicas" ]] || [[ "$available_replicas" -lt 1 ]] || [[ "$available_replicas" -lt "$total_replicas" ]]; then
        echo "Deployment '$deployment' is not fully available. Available replicas: $available_replicas, Total replicas: $total_replicas."
        return 1
      fi

      if ! kubectl get svc "$service" -n "$namespace" &>/dev/null; then
        echo "Service '$service' does not exist in namespace '$namespace'."
        return 1
      fi

      local running_pod_count
      running_pod_count=$(kubectl get pods -n "$namespace" --selector=app="$deployment" --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
      if [[ -z "$running_pod_count" ]] || [[ "$running_pod_count" -eq 0 ]]; then
        echo "No Pods for Deployment '$deployment' are in Running state in namespace '$namespace'."
        return 1
      fi

      echo "Namespace '$namespace', Deployment '$deployment', and Service '$service' are all running correctly."
      return 0
    }

    # Function to verify and install if necessary
    verify_and_install() {
      local namespace="$1"
      local deployment="$2"
      local service="$3"
      local install_function="$4"

      echo "Verifying Deployment '$deployment' and Service '$service' in namespace '$namespace'..."

      # Check the installation status file
      if [[ -f /tmp/skip_$install_function ]]; then
        echo "$install_function has already been executed. Skipping installation."
        return 0
      fi

      service_verify "$namespace" "$deployment" "$service"
      status=$?
      if [[ "$status" -eq 0 ]]; then
        echo -e "All checks for Deployment '$deployment' and Service '$service' passed.\n"
      else
        echo -e "Checks failed for Deployment '$deployment' and/or Service '$service' in namespace '$namespace'. Installing...\n"
        "$install_function"
      fi
    }

    # Loop through all kubeconfig files
    for KUBECONFIG in $(ls /.kube/config*); do
      echo "***************************************************************"
      export KUBECONFIG
      echo "Using KUBECONFIG: $KUBECONFIG"

      # Print current cluster context
      current_context=$(kubectl config current-context)
      echo -e "Current context: $current_context\n"

      # Prepare for Space Application creating...
      space_install

      # Verify and install Argo Workflows
      argo_install
      # verify_and_install "argo" "argo-server" "argo-server" "argo_install"
      verify_and_install "argo" "workflow-controller" "argo-server" "argo_install"

      # Verify and install Knative Serving
      knative_install
      # verify_and_install "knative-serving" "activator" "activator-service" "knative_install"
      verify_and_install "knative-serving" "autoscaler" "autoscaler" "knative_install"
      verify_and_install "knative-serving" "controller" "controller" "knative_install"
      verify_and_install "knative-serving" "net-kourier-controller" "net-kourier-controller" "knative_install"
      verify_and_install "knative-serving" "webhook" "webhook" "knative_install"

      # Verify and install Kourier networking components
      verify_and_install "kourier-system" "3scale-kourier-gateway" "kourier" "knative_install"
      verify_and_install "kourier-system" "3scale-kourier-gateway" "kourier-internal" "knative_install"

      # Verify and install LeaderWorkSet
      lws_install
      # Verify lws controller
      verify_and_install "lws-system" "lws-controller-manager" "lws-webhook-service" "lws_install"
      verify_and_install "lws-system" "lws-controller-manager" "lws-controller-manager-metrics-service" "lws_install"
    done
    {{- end }}
---
{{- end }}
