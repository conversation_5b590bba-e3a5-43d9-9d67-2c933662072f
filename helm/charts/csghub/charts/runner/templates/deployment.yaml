{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.global.deploy.enabled }}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  {{- with (include "common.annotations.deployment" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      {{- with (include "common.annotations.pod.checksum" (dict "context" . "configmap" true "secret" false)) }}
      annotations:
        {{- . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      dnsPolicy: None
      dnsConfig:
        nameservers:
          - {{ include "coredns.csghub" . }}
        searches:
          - {{ .Release.Namespace }}.svc.cluster.local
          - svc.cluster.local
          - cluster.local
        options:
          - name: ndots
            value: "5"
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      initContainers:
        - name: wait-for-postgresql
          image:  {{ or .Values.global.image.registry .Values.image.registry }}/opencsg/psql:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until pg_isready; do echo 'Wait for PostgreSQL to be ready'; sleep 2; done" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
          env:
            - name: PGHOST
              value: "$(STARHUB_DATABASE_HOST)"
            - name: PGPORT
              value: "$(STARHUB_DATABASE_PORT)"
            - name: PGDATABASE
              value: "$(STARHUB_DATABASE_NAME)"
            - name: PGUSER
              value: "$(STARHUB_DATABASE_USERNAME)"
            - name: PGPASSWORD
              value: "$(STARHUB_DATABASE_PASSWORD)"
        - name: wait-for-server
          image: {{ or .Values.global.image.registry .Values.image.registry }}/busybox:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until nc -z {{ include "server.internal.domain" . }} {{ include "server.internal.port" . }}; do echo 'Wait for csghub-server to be ready'; sleep 2; done" ]
      containers:
        - name: runner
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ or .Values.global.image.name .Values.image.repository }}:{{ include "csghub.image.tag" (dict "tag" (or .Values.global.image.tag .Values.image.tag) "context" .) }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "/starhub-bin/starhub", "deploy", "runner" ]
          ports:
            - containerPort: 8082
              name: runner
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" . }}
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "nats") }}
          env:
            - name: STARHUB_SERVER_DOCKER_REG_BASE
              value: {{ printf "%s/%s" (include "csghub.registry.repository" .) (include "csghub.registry.namespace" .) }}
            - name: STARHUB_SERVER_DOCKER_IMAGE_PULL_SECRET
              value: {{ include "common.names.custom" (list . "registry-docker-config") }}
            - name: OPENCSG_ACCOUNTING_NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" . }}:{{ include "nats.internal.ports.api" . }}"
            - name: STARHUB_SERVER_ARGO_S3_PUBLIC_BUCKET
              value: {{ include "csghub.objectStore.bucket" . }}
            {{- if not .Values.global.objectStore.external }}
            - name: STARHUB_SERVER_S3_ACCESS_KEY_ID
              value: "$(MINIO_ROOT_USER)"
            - name: STARHUB_SERVER_S3_ACCESS_KEY_SECRET
              value: "$(MINIO_ROOT_PASSWORD)"
            {{- end }}
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 8082
            initialDelaySeconds: 20
            periodSeconds: 10
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: kube-configs
              mountPath: /root/.kube
              readOnly: true
      volumes:
        - name: kube-configs
          secret:
            {{- $secret := or .Values.global.deploy.kubeSecret .Values.deployment.kubeSecret }}
            {{- $exists := lookup "v1" "Secret" .Release.Namespace $secret }}
            {{- if $exists }}
            secretName: {{ $secret }}
            {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
