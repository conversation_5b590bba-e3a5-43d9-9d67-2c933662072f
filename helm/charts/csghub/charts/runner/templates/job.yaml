{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.global.deploy.enabled .Values.global.deploy.autoConfigure }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "common.names.custom" (list . "post-configure") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: post-install, post-upgrade
    helm.sh/hook-weight: "2"
    helm.sh/hook-delete-policy: before-hook-creation
spec:
  ttlSecondsAfterFinished: 1200
  backoffLimit: 4
  activeDeadlineSeconds: 600
  template:
    metadata:
      name: {{ include "common.names.custom" . }}
      labels: {{ include "common.labels" . | nindent 8 }}
    spec:
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      restartPolicy: OnFailure
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      containers:
        - name: kubectl
          image: {{ or .Values.global.image.registry .Values.image.registry }}/bitnami/kubectl:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/bash", "/scripts/configure.sh" ]
          volumeMounts:
            - name: kube-configs
              mountPath: /.kube
            - name: configure
              mountPath: /scripts
              readOnly: false
            - name: argo
              mountPath: /argo
            - name: knative
              mountPath: /knative
            - name: lws
              mountPath: /lws
            - name: space
              mountPath: /space
        - name: gpu-gather
          image: {{ or .Values.global.image.registry .Values.autoLabel.image.registry }}/{{ .Values.autoLabel.image.repository }}:{{ .Values.autoLabel.image.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args: [ "-d", "/.kube", "-n", "default", "-i", "{{ or .Values.global.image.registry .Values.autoLabel.image.registry }}/{{ .Values.autoLabel.cli.image }}", "-c", "{{ .Values.autoLabel.cli.cmd }}" ]
          volumeMounts:
            - name: kube-configs
              mountPath: /.kube
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
          env:
            - name: PGHOST
              value: "$(STARHUB_DATABASE_HOST)"
            - name: PGPORT
              value: "$(STARHUB_DATABASE_PORT)"
            - name: PGDATABASE
              value: "$(STARHUB_DATABASE_NAME)"
            - name: PGUSER
              value: "$(STARHUB_DATABASE_USERNAME)"
            - name: PGPASSWORD
              value: "$(STARHUB_DATABASE_PASSWORD)"
      volumes:
        - name: kube-configs
          secret:
            {{- $secret := or .Values.global.deploy.kubeSecret .Values.deployment.kubeSecret }}
            {{- $exists := lookup "v1" "Secret" .Release.Namespace $secret }}
            {{- if $exists }}
            secretName: {{ $secret }}
            {{- end }}
        - name: configure
          configMap:
            name: {{ include "common.names.custom" . }}
        - name: argo
          configMap:
            name: {{ include "common.names.custom" (list . "runner-argo") }}
        - name: knative
          configMap:
            name: {{ include "common.names.custom" (list . "runner-knative") }}
        - name: lws
          configMap:
            name: {{ include "common.names.custom" (list . "runner-lws") }}
        - name: space
          configMap:
            name: {{ include "common.names.custom" (list . "runner-space") }}
{{- end }}
