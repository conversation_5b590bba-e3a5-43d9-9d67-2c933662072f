{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations: {{ .Values.annotations | toYaml | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      annotations:
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      containers:
        - name: celery-producer
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "sh", "-c", "cd /code/project && celery -A config beat --loglevel=INFO" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "web") }}
            {{- if not .Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "redis") }}
            {{- end }}
          env:
            {{- if not .Values.global.redis.external }}
            - name: REDIS_URL
{{/*              value: {{ printf ":$(REDIS_PASSWD)@%s" (include "csghub.redis.host" .) }}*/}}
              value: {{ printf ":@%s" (include "csghub.redis.host" .) }}
            {{- end }}
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /var/repo/gitrepos
              subPath: gitrepos
            - name: data
              mountPath: /code/data
              subPath: codedata
        - name: celery-worker-beat
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "sh", "-c", "cd /code/project && celery -A config worker -Q high_priority_queue -c 1 --loglevel=INFO" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "web") }}
            {{- if not .Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "redis") }}
            {{- end }}
          env:
            {{- if not .Values.global.redis.external }}
            - name: REDIS_URL
              value: {{ printf ":@%s" (include "csghub.redis.host" .) }}
{{/*              value: {{ printf ":$(REDIS_PASSWD)@%s" (include "csghub.redis.host" .) }}*/}}
            {{- end }}
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /var/repo/gitrepos
              subPath: gitrepos
            - name: data
              mountPath: /code/data
              subPath: codedata
        - name: celery-worker
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "sh", "-c", "cd /code/project && celery -A config worker --loglevel=INFO" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "web") }}
            {{- if not .Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "redis") }}
            {{- end }}
          env:
            {{- if not .Values.global.redis.external }}
            - name: REDIS_URL
              value: {{ printf ":@%s" (include "csghub.redis.host" .) }}
{{/*              value: {{ printf ":$(REDIS_PASSWD)@%s" (include "csghub.redis.host" .) }}*/}}
            {{- end }}
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /var/repo/gitrepos
              subPath: gitrepos
            - name: data
              mountPath: /code/data
              subPath: codedata
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: {{ printf "data-%s-%s-0" .Release.Name "web" }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}