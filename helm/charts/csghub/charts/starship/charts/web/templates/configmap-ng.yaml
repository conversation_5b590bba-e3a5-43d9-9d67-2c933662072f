{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" (list . "web-nginx") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    resource.dependencies/deployments: |
      {{ include "common.names.custom" . }}
data:
  nginx.conf: |
    user root;
    # May be equal to `grep processor /proc/cpuinfo | wc -l`
    worker_processes auto;
    worker_cpu_affinity auto;

    # PCRE JIT can speed up processing of regular expressions significantly.
    pcre_jit on;

    # error_log
    error_log  /var/log/nginx/error.log notice;

    events {
        # Should be equal to `ulimit -n`
        worker_connections 1024;

        # Let each process accept multiple connections.
        multi_accept on;

        # Preferred connection method for newer linux versions.
        use epoll;
    }

    http {
        # Disables the “Server” response header
        server_tokens off;
        charset utf-8;

        # Sendfile copies data between one FD and other from within the kernel.
        # More efficient than read() + write(), since the requires transferring
        # data to and from the user space.
        sendfile on;

        # Tcp_nopush causes nginx to attempt to send its HTTP response head in one
        # packet, instead of using partial frames. This is useful for prepending
        # headers before calling sendfile, or for throughput optimization.
        tcp_nopush on;

        # Don't buffer data-sends (disable Nagle algorithm). Good for sending
        # frequent small bursts of data in real time.
        #
        tcp_nodelay on;

        # http://nginx.org/en/docs/hash.html
        types_hash_max_size 4096;
        default_type application/octet-stream;

        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
        '$status $body_bytes_sent "$http_referer" '
        '"$http_user_agent" "$http_x_forwarded_for"';

        # Logging Settings
        access_log  /var/log/nginx/access.log  main;

        # Gzip Settings
        gzip on;
        gzip_disable "msie6";

        gzip_comp_level 6;
        # gzip_comp_level 9;
        gzip_min_length 1100;
        gzip_buffers 16 8k; gzip_proxied any;
        # gzip_http_version 1.1;
        gzip_types text/plain application/xml text/css text/js text/xml application/x-javascript text/javascript application/json application/xml+rss;

        keepalive_timeout 240;

        client_max_body_size 300M;

        # redirect http request to https
        #server {
        #    listen 80;
        #    server_name starchain.opencsg.com;
        #    rewrite  ^(.*)$  https://${server_name}$1 permanent;
        #}

        server {
            listen 80;
            server_name {{ include "starship.external.api.domain" . }};

            location /agentic {
                proxy_pass http://{{ include "agentic.internal.domain" . }}:{{ include "agentic.internal.port" . }};
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Host $server_name;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 300s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;

                # For streaming API (event-stream)
                proxy_buffering off;
                proxy_cache off;
            }

            location / {
                proxy_pass http://{{ include "web.internal.domain" . }}:{{ include "web.internal.port" . }};
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Host $server_name;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 300s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;

                # For streaming API (event-stream)
                proxy_buffering off;
                proxy_cache off;
            }


            error_page 500 502 503 504 /50x.html;
            location = /50x.html {
                root html;
            }
        }
    }

