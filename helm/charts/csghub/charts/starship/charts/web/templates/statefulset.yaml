{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations: {{ .Values.annotations | toYaml | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  serviceName: {{ include "common.names.custom" . }}
  replicas: 1
  minReadySeconds: 120
  template:
    metadata:
      annotations:
        checksum/configmap1: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/configmap2: {{ include (print $.Template.BasePath "/configmap-ng.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      containers:
        - name: web
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "sh", "-c", "cp -a /code/project/static /usr/share/ && /code/start.sh" ]
          ports:
            - containerPort: 8000
              name: web
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" . }}
            - configMapRef:
                name: {{ include "common.names.custom" (list . "casdoor") }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "nats") }}
            {{- if not .Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "redis") }}
            {{- end }}
          env:
            - name: CASDOOR_CLIENT_ID
              value: "$(STARSHIP_SERVER_CASDOOR_CLIENT_ID)"
            - name: CASDOOR_SECRET_KEY
              value: "$(STARSHIP_SERVER_CASDOOR_CLIENT_SECRET)"
            {{- if not .Values.global.redis.external }}
            - name: REDIS_URL
              value: {{ printf ":@%s" (include "csghub.redis.host" .) }}
{{/*              value: {{ printf ":$(REDIS_PASSWD)@%s" (include "csghub.redis.host" .) }}*/}}
            {{- end }}
            - name: NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" . }}:{{ include "nats.internal.ports.api" . }}"
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 8000
            initialDelaySeconds: 120
            periodSeconds: 10
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /var/repo/gitrepos
              subPath: gitrepos
            - name: data
              mountPath: /code/data
              subPath: codedata
        - name: nginx
          image: {{ or .Values.global.image.registry .Values.image.registry }}/nginx:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 80
              name: nginx
              protocol: TCP
          {{- with .Values.environments }}
          env:
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 5
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
      volumes:
        - name: config
          configMap:
            name: {{ include "common.names.custom" (list . "web-nginx") }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: data
        namespace: {{ .Release.Namespace }}
        labels:
          {{- include "common.labels" . | nindent 10 }}
        annotations:
          helm.sh/resource-policy: keep
          {{- with .Values.annotations }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
      spec:
        accessModes: {{ .Values.persistence.accessMode }}
        {{- if .Values.persistence.storageClass }}
        storageClassName: {{ .Values.persistence.storageClass }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size }}