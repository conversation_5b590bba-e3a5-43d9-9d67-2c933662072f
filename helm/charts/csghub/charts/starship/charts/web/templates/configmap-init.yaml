{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" (list . "web-init") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  init.sql: |
    -- LLM
    INSERT INTO platforms_llmmodel
      (id, provider, model, context_window, api_key, api_base, api_version, short_name)
    VALUES
      (1, 'custom', 'openai/data/models/deepseek-coder-33b-instruct-awq', 16000, 'dell-123', 'http://host:port/v1', '', '')
    ON CONFLICT (id) DO NOTHING;

    SELECT
    pg_catalog.setval('public.platforms_llmmodel_id_seq', (
            SELECT
                MAX(id)
            FROM public.platforms_llmmodel), TRUE);

    INSERT INTO platforms_llmsetting
      (id, feature, model_id)
    VALUES
      (1, '', 1),
      (2, 'ide', 1),
      (3, 'ide-chat', 1),
      (4, 'ide-code-completion', 1),
      (5, 'ide-codereview', 1)
    ON CONFLICT (id) DO NOTHING;

    SELECT
    pg_catalog.setval('public.platforms_llmsetting_id_seq', (
            SELECT
                MAX(id)
            FROM public.platforms_llmsetting), TRUE);

    -- Embedding
    INSERT INTO platforms_embeddingmodel
      (id, provider, model, dim, api_key, api_base, api_version)
    VALUES
      (1, 'custom', 'openai/jina-embeddings-v2-base-code', 768, 'dell-123', 'http://host:port/v1', '')
    ON CONFLICT (id) DO NOTHING;

    SELECT
    pg_catalog.setval('public.platforms_embeddingmodel_id_seq', (
            SELECT
                MAX(id)
            FROM public.platforms_embeddingmodel), TRUE);

    INSERT INTO platforms_embeddingsetting
      (id, feature, model_id)
    VALUES
      (1, '', 1),
      (2, 'ide', 1)
    ON CONFLICT (id) DO NOTHING;

    SELECT
    pg_catalog.setval('public.platforms_embeddingsetting_id_seq', (
            SELECT
                MAX(id)
            FROM public.platforms_embeddingsetting), TRUE);

    -- Casdoor
    INSERT INTO socialaccount_socialapp
      (id, provider, name, client_id, secret, key, provider_id, settings)
    VALUES
      (1, 'casdoor', 'casdoor', '922b009c161d8efb3422', 'f55bd1917a52a24080f831a31010157e50f17f5a', '', 'casdoor', '{"SCOPE": ["profile"]}')
    ON CONFLICT (id) DO NOTHING;

    SELECT
    pg_catalog.setval('public.socialaccount_socialapp_id_seq', (
            SELECT
                MAX(id)
            FROM public.socialaccount_socialapp), TRUE);

    -- point to django_site
    INSERT INTO socialaccount_socialapp_sites
      (id, socialapp_id, site_id)
    VALUES
      (2, 1, 1)
    ON CONFLICT (id) DO NOTHING;

    SELECT
    pg_catalog.setval('public.socialaccount_socialapp_sites_id_seq', (
            SELECT
                MAX(id)
            FROM public.socialaccount_socialapp_sites), TRUE);

    -- Change django_site to the correct domain
    UPDATE django_site SET domain = '{{ include "starship.external.api.domain" . }}', name = '{{ include "starship.external.api.domain" . }}' WHERE id = 1;

    -- Init Admin user
    INSERT INTO accounts_user
      (password, is_superuser, username, first_name, last_name, email, is_staff, is_active, date_joined)
    VALUES
      ('pbkdf2_sha256$720000$ADQralA3c5ajFecfQBZONu$uNE3YstFZ3Ezq9LQGHyPsA2Lj/k6w4QlSpq5apqH6p4=', 't', 'admin', '','', '<EMAIL>', 't', 't', now())
    ON CONFLICT (username) DO NOTHING;

    SELECT
    pg_catalog.setval('public.accounts_user_id_seq', (
            SELECT
                MAX(id)
            FROM public.accounts_user), TRUE);

