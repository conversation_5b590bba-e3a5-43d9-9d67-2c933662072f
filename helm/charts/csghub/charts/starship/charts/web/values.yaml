## Default values for Starship-Web.
## This is a YAML-formatted file.
## Declare variables to be passed into your templates.
## Enable or disable the Starship-Web component.
enabled: true

## Configuration for images, it can be overwritten by global.images
image:
  ## List of image pull secrets.
  ## Used to pull Docker images from private repositories.
  ## This array is empty by default, meaning no secrets are required by default.
  pullSecrets: []
  ## Specify path prefix relative to docker.io
  ## eg: minio/minio:latest with prefix {{ prefix }}/minio/minio:latest
  ## No need to add the final slash `/`
  registry: "opencsg-registry.cn-beijing.cr.aliyuncs.com/opencsg_public"
  ## Specifies the location of the Registry Docker image in the registry.
  repository: "starship-web"
  ## Defines the specific version of the Registry image to use.
  tag: "v0.2.0"
  ## Determines how the image should be pulled from the registry.
  pullPolicy: "IfNotPresent"

service:
  ## This determines how the Accounting service is accessed within the cluster or from external sources.
  type: ClusterIP
  ## This is the network port where the Accounting service will listen for connections.
  port: 8000

## Specifies the location and credentials for accessing the external Redis cache.
redis:
  ## host for accessing Redis
  host: ""
  ## Port for accessing Redis
  port: 6379
  ## Password for accessing Redis
  password: ""

## Specifies the location and credentials for accessing the external PostgreSQL database.
postgresql:
  ## Specifies the host address of the PostgreSQL database server.
  host: ""
  ## Defines the port on which the PostgreSQL server is listening.
  port: 5432
  ## The username used for authentication with the PostgreSQL database.
  user: "starship_codegpt"
  ## The password used for authentication with the PostgreSQL database.
  ## It is empty by default for security reasons and should be set through secure means.
  password: ""
  ## The name of the database to connect to on the PostgreSQL server.
  database: "starship_codegpt"
  ## Sets the timezone for the database connection, ensuring time-based operations use the correct timezone.
  timezone: "Etc/UTC"

## OpenAI Configuration
openai:
  ## Specifies the default AI model to be used for OpenAI requests
  ## Example: "csg-gpt4o-mini" (custom model) or standard OpenAI models like "gpt-4"
  model: "csg-gpt4o-mini"
  ## API connection settings for Azure OpenAI service
  api:
    ## Base endpoint URL for Azure OpenAI API
    ## Note: This should point to your Azure OpenAI service instance
    base: "https://opencsg-us.openai.azure.com"
    ## API version to use for compatibility
    ## Important: This should match the API version supported by your Azure OpenAI deployment
    version: "2024-06-01"
    ## API key for authentication (keep empty in version control)
    ## Security Note: Always set this via environment variables or secrets in production
    ## Example: "123456abcdef7890" (actual key should never be committed to source control)
    key: ""

## Persistence settings for the Starship-Web data.
persistence:
  ## Specifies the StorageClass used for provisioning the volume.
  ## An empty value means the default StorageClass is used.
  ## StorageClass defines the type of storage used and can affect performance and cost.
  storageClass: ""
  ## Defines the access modes of the volume.
  ## ReadWriteOnce means the volume can be mounted as read-write by a single node.
  ## This is suitable for most use cases where a single instance of Starship-Web is running.
  accessMode: ["ReadWriteMany"]
  ## Specifies the size of the persistent volume.
  ## This should be adjusted based on expected usage and data growth over time.
  size: 10Gi

serviceAccount:
  ## Determines whether a service account should be created.
  create: false
  ## Controls whether the service account token should be automatically mounted.
  automount: false
  ## Allows for annotations to be added to the service account.
  annotations: {}

## podAnnotations: Allows you to add annotations to the pods. Annotations can be used to attach arbitrary -
## non-identifying metadata to objects. Tools and libraries can retrieve this metadata.
podAnnotations: {}

## podLabels: Provides the ability to add labels to the pods. Labels are key/value pairs that are attached to objects, -
## such as pods, which can be used for the purposes of organization and to select subsets of objects.
podLabels: {}

## podSecurityContext: Defines security settings for the entire pod. This can include settings like the user and group -
## IDs that processes run as, and privilege and access control settings.
podSecurityContext: {}

## securityContext: Specifies security settings for a specific container within a pod. This can include settings such as -
## capabilities, security enhanced Linux (SELinux) options, and whether the container should run as privileged.
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

## environments: This section is reserved for defining environment variables for the Starship-Web container.
## Environment variables can be used to customize the behavior of the Starship-Web instance.
## For example, you might use environment variables to configure logging levels or to enable certain Starship-Web features.
## This section is currently empty, indicating that no environment variables have been explicitly set.
environments: {}

## annotations: This section allows you to add annotations to the Starship-Web deployment.
## Annotations are key-value pairs that can be used to store additional metadata about the deployment.
## This can be useful for tools and applications that interact with your Kubernetes cluster, providing them with extra -
## information about your Starship-Web instance.
## Like the environments section, this is also currently empty.
annotations: {}
#  helm.sh/resource-policy: keep

## The 'resources' section is used to define the compute resource requirements for the Starship-Web container.
## Here, you can specify the minimum and maximum amount of CPU and memory that the container is allowed to use.
## Leaving this section empty means that no specific resource limits or requests are set for the Starship-Web container.
## This approach can be beneficial in environments with limited resources, such as development or testing environments,
## where you might not want to enforce strict resource constraints.
## However, for production environments, it's recommended to uncomment and set these values to ensure that the Starship-Web container
## has enough resources to operate efficiently and to prevent it from consuming too much of the available resources on the node.
## 'limits' specify the maximum amount of CPU and memory the container can use.
## 'requests' specify the minimum amount of CPU and memory guaranteed to the container.
## If these values are not set, the container could be terminated in a resource-constrained environment or it might not perform as expected.
resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

## nodeSelector: This section allows you to specify node labels for pod assignment.
## This is useful for ensuring that pods are only scheduled on nodes with specific labels.
nodeSelector: {}

## tolerations: This section allows you to specify tolerations for the pods.
## Tolerations enable the pods to schedule onto nodes with matching taints.
## This is useful in scenarios where you want to ensure that certain workloads run on dedicated nodes.
tolerations: []

## affinity: This section allows you to set rules that affect how pods are scheduled based on various criteria -
## including labels of pods that are already running on the node.
## Affinity settings can be used to ensure that certain pods are co-located in the same node, zone, etc., or to -
## spread pods across nodes or zones for high availability.
affinity: {}
