{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.enabled }}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations: {{ .Values.annotations | toYaml | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      containers:
        - name: billing
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "sh", "-c", "sh /tmp/config.sh && /app/billing -config=/app/config/config.yaml" ]
          ports:
            - containerPort: 8080
              name: billing
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" . }}
            - configMapRef:
                name: {{ include "common.names.custom" (list . "web") }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "nats") }}
            {{- if not .Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list . "redis") }}
            {{- end }}
          env:
            - name: NATS_URL
              value: nats://$(NATS_USERNAME):$(NATS_PASSWORD)@csghub-nats:4222
            {{- if not .Values.global.redis.external }}
            - name: REDIS_URL
              value: {{ printf ":@%s" (include "csghub.redis.host" .) }}
{{/*              value: {{ printf ":$(REDIS_PASSWD)@%s" (include "csghub.redis.host" .) }}*/}}
            {{- end }}
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 20
            periodSeconds: 10
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /tmp
              readOnly: false
      volumes:
        - name: config
          configMap:
            name: {{ include  "common.names.custom" . }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}