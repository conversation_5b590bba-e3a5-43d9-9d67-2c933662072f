{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  config.sh: |
    #!/bin/sh
    mkdir -p /app/config
    cat <<EOF > /app/config/config.yaml
    nats: $NATS_URL
    pub:
      addr: :8080
      key: ''
    sub:
      streams:
        - name: accountingNotifyStream
          consumers:
            - durable_name: starship-billing-durable-consumer
      webhook:
        url: http://{{ include "web.internal.domain" . }}:{{ include "web.internal.port" . }}/api/v1/platforms/billing-events/
        key: 'key'
    EOF
{{- end }}