## Default values for Starship-Billing.
## This is a YAML-formatted file.
## Declare variables to be passed into your templates.
enabled: true

## Define deploy replicas
replicas: 1

## Configuration for images, it can be overwritten by global.images
image:
  ## List of image pull secrets.
  ## Used to pull Docker images from private repositories.
  ## This array is empty by default, meaning no secrets are required by default.
  pullSecrets: []
  ## Specify path prefix relative to docker.io
  ## eg: minio/minio:latest with prefix {{ prefix }}/minio/minio:latest
  ## No need to add the final slash `/`
  registry: "opencsg-registry.cn-beijing.cr.aliyuncs.com/opencsg_public"
  ## Specifies the location of the Registry Docker image in the registry.
  repository: "starship-billing"
  ## Defines the specific version of the Registry image to use.
  tag: "latest"
  ## Determines how the image should be pulled from the registry.
  pullPolicy: "IfNotPresent"

service:
  ## This determines how the Accounting service is accessed within the cluster or from external sources.
  type: ClusterIP
  ## This is the network port where the Accounting service will listen for connections.
  port: 8080

serviceAccount:
  ## Determines whether a service account should be created.
  create: false
  ## Controls whether the service account token should be automatically mounted.
  automount: false
  ## Allows for annotations to be added to the service account.
  annotations: {}

## Specifies the location and credentials for accessing the external Redis cache.
redis:
  ## host for accessing Redis
  host: ""
  ## Port for accessing Redis
  port: 6379
  ## Password for accessing Redis
  password: ""

## podAnnotations: Allows you to add annotations to the pods. Annotations can be used to attach arbitrary -
## non-identifying metadata to objects. Tools and libraries can retrieve this metadata.
podAnnotations: {}

## podLabels: Provides the ability to add labels to the pods. Labels are key/value pairs that are attached to objects, -
## such as pods, which can be used for the purposes of organization and to select subsets of objects.
podLabels: {}

## podSecurityContext: Defines security settings for the entire pod. This can include settings like the user and group -
## IDs that processes run as, and privilege and access control settings.
podSecurityContext:
  ## Allow the pod to run in privileged mode (true or false)
  privileged: true
  ## Allow privilege escalation (true or false)
  allowPrivilegeEscalation: true
  ## Set the root filesystem of the pod to read-only (true or false)
  readOnlyRootFilesystem: false

## securityContext: Specifies security settings for a specific container within a pod. This can include settings such as -
## capabilities, security enhanced Linux (SELinux) options, and whether the container should run as privileged.
securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  runAsUser: 0
  runAsGroup: 0
  fsGroup: 0

## environments: This section is reserved for defining environment variables for the Accounting container.
## Environment variables can be used to customize the behavior of the Accounting instance.
## For example, you might use environment variables to configure logging levels or to enable certain Accounting features.
## This section is currently empty, indicating that no environment variables have been explicitly set.
environments: {}

## annotations: This section allows you to add annotations to the Accounting deployment.
## Annotations are key-value pairs that can be used to store additional metadata about the deployment.
## This can be useful for tools and applications that interact with your Kubernetes cluster, providing them with extra -
## information about your Accounting instance.
## Like the environments section, this is also currently empty.
annotations: {}
#  helm.sh/resource-policy: keep

## The 'resources' section is used to define the compute resource requirements for the Accounting container.
## Here, you can specify the minimum and maximum amount of CPU and memory that the container is allowed to use.
## Leaving this section empty means that no specific resource limits or requests are set for the Accounting container.
## This approach can be beneficial in environments with limited resources, such as development or testing environments,
## where you might not want to enforce strict resource constraints.
## However, for production environments, it's recommended to uncomment and set these values to ensure that the Accounting container
## has enough resources to operate efficiently and to prevent it from consuming too much of the available resources on the node.
## 'limits' specify the maximum amount of CPU and memory the container can use.
## 'requests' specify the minimum amount of CPU and memory guaranteed to the container.
## If these values are not set, the container could be terminated in a resource-constrained environment or it might not perform as expected.
resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

## nodeSelector: This section allows you to specify node labels for pod assignment.
## This is useful for ensuring that pods are only scheduled on nodes with specific labels.
nodeSelector: {}

## tolerations: This section allows you to specify tolerations for the pods.
## Tolerations enable the pods to schedule onto nodes with matching taints.
## This is useful in scenarios where you want to ensure that certain workloads run on dedicated nodes.
tolerations: []

## affinity: This section allows you to set rules that affect how pods are scheduled based on various criteria -
## including labels of pods that are already running on the node.
## Affinity settings can be used to ensure that certain pods are co-located in the same node, zone, etc., or to -
## spread pods across nodes or zones for high availability.
affinity: {}

## autoscaling: This section configures the Horizontal Pod Autoscaler (HPA) for the Accounting deployment.
## The HPA automatically scales the number of pods in a deployment, replication controller, replica set, or stateful -
## set based on observed CPU utilization.
autoscaling:
  ## Determines whether autoscaling is enabled. Set to true to enable autoscaling.
  enabled: false
  ## The minimum number of replicas. The autoscaler will not scale below this number.
  minReplicas: 1
  ## The maximum number of replicas. The autoscaler will not scale above this number.
  maxReplicas: 100
  ## The target average CPU utilization (represented as a percentage) over all the pods. When the average CPU utilization -
  ## exceeds this threshold, the HPA will scale up.
  targetCPUUtilizationPercentage: 80
  ## Uncomment to enable scaling based on memory usage. This sets the target average memory utilization over all the pods.
  # targetMemoryUtilizationPercentage: 80
