{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

  {{- if .Values.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  config.js: |
    window.envConfig = {
        VITE_APP_BASE_WEB: {{ include "starship.external.endpoint" . | squote }},
        VITE_APP_BASE_API: {{ include "starship.external.api.endpoint" . | squote }},
        VITE_APP_LOGIN_URL: {{ printf "%s/login/oauth/authorize?client_id=922b009c161d8efb3422&response_type=code&redirect_uri=%s/api/v1/account/casdoor/login/callback&scope=profile&state=casdoor" (include "casdoor.external.endpoint" .) (include "starship.external.api.endpoint" .) | squote }}
    };
  default.conf: |
    server {
        listen 80;
        server_name {{ include "starship.external.domain" . }};


        # 上传文件大小限制
        client_max_body_size 3000m;
        # 开启gzip压缩
        gzip on;
        gzip_disable "MSIE [1-6].";
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_buffers 16 8k;
        gzip_http_version 1.0;
        gzip_min_length 256;
        gzip_types application/atom+xml application/geo+json application/javascript application/x-javascript application/json application/ld+json application/manifest+json application/rdf+xml application/rss+xml application/xhtml+xml application/xml font/eot font/otf font/ttf image/svg+xml text/css text/javascript text/plain text/xml;

        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri /index.html;
        }

        location @router {
            rewrite ^.*$ /index.html last;
        }
    }
{{- end }}