## Default values for PostgreSQL.
## This is a YAML-formatted file.
## Declare variables to be passed into your templates.
## Enable or disable the PostgreSQL component.
enabled: true

# Configuration for images, it can be overwritten by global.images
image:
  ## List of image pull secrets.
  ## Used to pull Docker images from private repositories.
  ## This array is empty by default, meaning no secrets are required by default.
  pullSecrets: []
  ## Specify path prefix relative to docker.io
  ## eg: minio/minio:latest with prefix {{ prefix }}/minio/minio:latest
  ## No need to add the final slash `/`
  registry: "opencsg-registry.cn-beijing.cr.aliyuncs.com/opencsg_public"
  ## Specifies the location of the PostgreSQL Docker image in the registry.
  repository: postgres
  ## Defines the specific version of the PostgreSQL image to use.
  tag: "15.10"
  ## Determines how the image should be pulled from the registry.
  pullPolicy: "IfNotPresent"

service:
  ## This determines how the PostgreSQL service is accessed within the cluster or from external sources.
  type: ClusterIP
  ## This is the network port where the PostgreSQL service will listen for connections.
  port: 5432

## You can pass in the parameters you want to modify by executing this parameter.
## Currently, only hot loading parameters are supported.
parameters: { }
  ## Set the parameters for the PostgreSQL database
  ## The online reload parameter can take effect automatically.
  ## If you need to restart the database, please restart it manually.
  # timezone: "Etc/UTC"

## List of databases to be created upon deployment
databases:
  - csghub_portal
  - csghub_server
  - csghub_casdoor
  - csghub_temporal
  - csghub_temporal_visibility
  - csghub_dataflow
  - starship_codegpt

## Persistence settings for the PostgreSQL data.
persistence:
  ## Specifies the StorageClass used for provisioning the volume.
  ## An empty value means the default StorageClass is used.
  ## StorageClass defines the type of storage used and can affect performance and cost.
  storageClass: ""
  ## Defines the access modes of the volume.
  ## ReadWriteOnce means the volume can be mounted as read-write by a single node.
  ## This is suitable for most use cases where a single instance of PostgreSQL is running.
  accessMode: ["ReadWriteOnce"]
  ## Specifies the size of the persistent volume.
  ## This should be adjusted based on expected usage and data growth over time.
  size: 50Gi

serviceAccount:
  ## Determines whether a service account should be created.
  create: false
  ## Controls whether the service account token should be automatically mounted.
  automount: false
  ## Allows for annotations to be added to the service account.
  annotations: {}

## podAnnotations: Allows you to add annotations to the pods. Annotations can be used to attach arbitrary -
## non-identifying metadata to objects. Tools and libraries can retrieve this metadata.
podAnnotations: {}

## podLabels: Provides the ability to add labels to the pods. Labels are key/value pairs that are attached to objects, -
## such as pods, which can be used for the purposes of organization and to select subsets of objects.
podLabels: {}

## podSecurityContext: Defines security settings for the entire pod. This can include settings like the user and group -
## IDs that processes run as, and privilege and access control settings.
podSecurityContext: {}

## securityContext: Specifies security settings for a specific container within a pod. This can include settings such as -
## capabilities, security enhanced Linux (SELinux) options, and whether the container should run as privileged.
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

## environments: This section is reserved for defining environment variables for the PostgreSQL container.
## Environment variables can be used to customize the behavior of the PostgreSQL instance.
## For example, you might use environment variables to configure logging levels or to enable certain PostgreSQL features.
## This section is currently empty, indicating that no environment variables have been explicitly set.
environments: {}

## annotations: This section allows you to add annotations to the PostgreSQL deployment.
## Annotations are key-value pairs that can be used to store additional metadata about the deployment.
## This can be useful for tools and applications that interact with your Kubernetes cluster, providing them with extra -
## information about your PostgreSQL instance.
## Like the environments section, this is also currently empty.
annotations: {}
#  helm.sh/resource-policy: keep

## The 'resources' section is used to define the compute resource requirements for the PostgreSQL container.
## Here, you can specify the minimum and maximum amount of CPU and memory that the container is allowed to use.
## Leaving this section empty means that no specific resource limits or requests are set for the PostgreSQL container.
## This approach can be beneficial in environments with limited resources, such as development or testing environments,
## where you might not want to enforce strict resource constraints.
## However, for production environments, it's recommended to uncomment and set these values to ensure that the PostgreSQL container
## has enough resources to operate efficiently and to prevent it from consuming too much of the available resources on the node.
## 'limits' specify the maximum amount of CPU and memory the container can use.
## 'requests' specify the minimum amount of CPU and memory guaranteed to the container.
## If these values are not set, the container could be terminated in a resource-constrained environment or it might not perform as expected.
resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

## nodeSelector: This section allows you to specify node labels for pod assignment.
## This is useful for ensuring that pods are only scheduled on nodes with specific labels.
nodeSelector: {}

## tolerations: This section allows you to specify tolerations for the pods.
## Tolerations enable the pods to schedule onto nodes with matching taints.
## This is useful in scenarios where you want to ensure that certain workloads run on dedicated nodes.
tolerations: []

## affinity: This section allows you to set rules that affect how pods are scheduled based on various criteria -
## including labels of pods that are already running on the node.
## Affinity settings can be used to ensure that certain pods are co-located in the same node, zone, etc., or to -
## spread pods across nodes or zones for high availability.
affinity: {}
