{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.enabled }}
apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations: {{ .Values.annotations | toYaml | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  serviceName: {{ include "common.names.custom" . }}
  replicas: 1
  minReadySeconds: 30
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      initContainers:
        - name: init-host-keys
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "/scripts/init_host_keys.sh" ]
          volumeMounts:
            - name: config
              mountPath: /scripts/init_host_keys.sh
              subPath: init_host_keys.sh
            - name: data
              mountPath: /srv/gitlab-shell/keys
      containers:
        - name: gitlab-shell
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/bash", "-c", "/scripts/process-wrapper" ]
          ports:
            - containerPort: 22
              name: gitlab-shell
              protocol: TCP
          env:
            - name: KEYS_DIRECTORY
              value: "/srv/gitlab-shell/keys"
            - name: SSH_DAEMON
              value: "gitlab-sshd"
            {{- with .Values.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 22
            initialDelaySeconds: 20
            periodSeconds: 10
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /srv/gitlab-shell/config.yml
              subPath: config.yml
            - name: config
              mountPath: /srv/gitlab-shell/.gitlab_shell_secret
              subPath: .gitlab_shell_secret
            - name: data
              mountPath: /srv/gitlab-shell/keys
      volumes:
        - name: config
          configMap:
            name: {{ include "common.names.custom" . }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: data
        namespace: {{ .Release.Namespace }}
        labels:
          {{- include "common.labels" . | nindent 10 }}
        annotations:
          helm.sh/resource-policy: keep
          {{- with .Values.annotations }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
      spec:
        accessModes: {{ or .Values.global.persistence.accessMode .Values.persistence.accessMode }}
        {{- if .Values.persistence.storageClass }}
        storageClassName: {{ or .Values.global.persistence.storageClass .Values.persistence.storageClass }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size }}
{{- end }}