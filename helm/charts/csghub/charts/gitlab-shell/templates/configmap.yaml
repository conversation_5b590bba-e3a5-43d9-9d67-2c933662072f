{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  .gitlab_shell_secret: {{ include "csghub.gitaly.token" . }}
  init_host_keys.sh: |
    #!/bin/sh
    # Check if there are any files starting with ssh_host_ in the /srv/gitlab-shell/keys directory
    if ! find /srv/gitlab-shell/keys -name 'ssh_host_*' | grep -q .; then
      # Generate SSH host keys, including common key types (RSA, ECDSA, ED25519)
      ssh-keygen -A || { echo "Failed to generate SSH host keys"; exit 1; }
      # Copy the generated SSH host keys to the specified directory
      cp /etc/ssh/ssh_host_* /srv/gitlab-shell/keys/ || { echo "Failed to copy SSH host keys"; exit 1; }
    fi
  config.yml: |
    #
    # If you change this file in a Merge Request, please also create
    # a Merge Request on https://gitlab.com/gitlab-org/omnibus-gitlab/merge_requests
    #

    # GitLab user. git by default
    user: git

    # URL to GitLab instance, used for API calls. Default: http://localhost:8080.
    # For relative URL support read http://doc.gitlab.com/ce/install/relative_url.html
    # You only have to change the default if you have configured Unicorn
    # to listen on a custom port, or if you have configured Unicorn to
    # only listen on a Unix domain socket. For Unix domain sockets use
    # "http+unix://<urlquoted-path-to-socket>", e.g.
    # "http+unix://%2Fpath%2Fto%2Fsocket"
    gitlab_url: {{ include "server.internal.endpoint" . }}

    # When a http+unix:// is used in gitlab_url, this is the relative URL root to GitLab.
    # Not used if gitlab_url is http:// or https://.
    # gitlab_relative_url_root: "/"

    # See installation.md#using-https for additional HTTPS configuration details.
    http_settings:
    #  read_timeout: 300
    #  user: someone
    #  password: somepass
    #  ca_file: /etc/ssl/cert.pem
    #  ca_path: /etc/pki/tls/certs
    #

    # File used as authorized_keys for gitlab user
    auth_file: "/home/<USER>/.ssh/authorized_keys"

    # SSL certificate dir where custom certificates can be placed
    # https://golang.org/pkg/crypto/x509/
    # ssl_cert_dir: /opt/gitlab/embedded/ssl/certs/

    # File that contains the secret key for verifying access to GitLab.
    # Default is .gitlab_shell_secret in the gitlab-shell directory.
    # secret_file: "/home/<USER>/gitlab-shell/.gitlab_shell_secret"
    #
    # The secret field supersedes the secret_file, and if set that
    # file will not be read.
    secret: "signing-key"

    # Log file.
    # Default is gitlab-shell.log in the root directory.
    log_file: "/dev/stdout"

    # Log level. INFO by default
    log_level: INFO

    # Log format. 'json' by default, can be changed to 'text' if needed
    log_format: json

    # Audit usernames.
    # Set to true to see real usernames in the logs instead of key ids, which is easier to follow, but
    # incurs an extra API call on every gitlab-shell command.
    audit_usernames: true

    # Distributed Tracing. GitLab-Shell has distributed tracing instrumentation.
    # For more details, visit https://docs.gitlab.com/ee/development/distributed_tracing.html
    # gitlab_tracing: opentracing://driver

    # This section configures the built-in SSH server. Ignored when running on OpenSSH.
    sshd:
      # Address which the SSH server listens on. Defaults to [::]:22.
      listen: "[::]:22"
      # Set to true if gitlab-sshd is being fronted by a load balancer that implements
      # the PROXY protocol.
      proxy_protocol: false
      # Proxy protocol policy ("use", "require", "reject", "ignore"), "use" is the default value
      # Values: https://github.com/pires/go-proxyproto/blob/195fedcfbfc1be163f3a0d507fac1709e9d81fed/policy.go#L20
      proxy_policy: "use"
      # Proxy allowed IP addresses. Takes precedent over proxy_policy. Disabled by default.
      # proxy_allowed:
      #  - "***********"
      #  - "***********/24"
      # Address which the server listens on HTTP for monitoring/health checks. Defaults to localhost:9122.
      web_listen: "localhost:9122"
      # Maximum number of concurrent sessions allowed on a single SSH connection. Defaults to 10.
      concurrent_sessions_limit: 10
      # Sets an interval after which server will send keepalive message to a client. Defaults to 15s.
      client_alive_interval: 15
      # The server waits for this time for the ongoing connections to complete before shutting down. Defaults to 10s.
      grace_period: 10
      # The server disconnects after this time if the user has not successfully logged in. Defaults to 60s.
      login_grace_time: 60
      # A short timeout to decide to abort the connection if the protocol header is not seen within it. Defaults to 500ms
      proxy_header_timeout: 500ms
      # The endpoint that returns 200 OK if the server is ready to receive incoming connections; otherwise, it returns 503 Service Unavailable. Defaults to "/start".
      readiness_probe: "/start"
      # The endpoint that returns 200 OK if the server is alive. Defaults to "/health".
      liveness_probe: "/health"
      # Specifies the available message authentication code algorithms that are used for protecting data integrity
      macs: [ <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1 ]
      # Specifies the available Key Exchange algorithms
      kex_algorithms: [ curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1 ]
      # Specified the ciphers allowed
      ciphers: [ <EMAIL>, <EMAIL>, <EMAIL>, aes128-ctr, aes192-ctr,aes256-ctr ]
      # Specified the available Public Key algorithms
      public_key_algorithms: [ ssh-rsa, ssh-dss, ecdsa-sha2-nistp256, <EMAIL>, ecdsa-sha2-nistp384, ecdsa-sha2-nistp521, ssh-ed25519, <EMAIL>, rsa-sha2-256, rsa-sha2-512 ]
      # SSH host key files.
      host_key_files:
        - /srv/gitlab-shell/keys/ssh_host_rsa_key
        - /srv/gitlab-shell/keys/ssh_host_ecdsa_key
        - /srv/gitlab-shell/keys/ssh_host_ed25519_key
      host_key_certs:
        - /srv/gitlab-shell/keys/ssh_host_rsa_key.pub
        - /srv/gitlab-shell/keys/ssh_host_ecdsa_key.pub
        - /srv/gitlab-shell/keys/ssh_host_ed25519_key.pub
      # GSSAPI-related settings
      gssapi:
        # Enable the gssapi-with-mic authentication method. Defaults to false.
        enabled: false
        # Keytab path. Defaults to "", system default (usually /etc/krb5.keytab).
        keytab: ""
        # The Kerberos service name to be used by sshd. Defaults to "", accepts any service name in keytab file.
        service_principal_name: ""

    lfs:
      # https://gitlab.com/groups/gitlab-org/-/epics/11872, disabled by default.
      pure_ssh_protocol: false
  {{- end }}