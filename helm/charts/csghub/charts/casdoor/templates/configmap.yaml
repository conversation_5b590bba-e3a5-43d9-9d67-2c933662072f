{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.enabled }}
{{- $password := include "casdoor.initPass" (durationRound now) }}
{{- $htpasswd := htpasswd "root" $password | trimPrefix "root:" }}
{{- $secretData := (lookup "v1" "Secret" .Release.Namespace (include "common.names.custom" (list . "init-root"))).data }}
{{- if $secretData }}
{{- $secretPass := index $secretData "INIT_ROOT_PASSWORD" }}
{{- if $secretPass }}
{{- $password = $secretPass | b64dec }}
{{- end }}
{{- $secretHtpasswd := index $secretData "INIT_ROOT_HTPASSWD" }}
{{- if $secretHtpasswd }}
{{- $htpasswd = $secretHtpasswd | b64dec }}
{{- end }}
{{- end }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    resource.dependencies/deployments: |
      {{ include "common.names.custom" . }}
      {{ include "common.names.custom" (list . "portal") }}
      {{ include "common.names.custom" (list . "user") }}
data:
  STARHUB_SERVER_CASDOOR_CLIENT_ID: "7a97bc5168cb75ffc514"
  STARHUB_SERVER_CASDOOR_CLIENT_SECRET: "33bd85106818efd90c57fb35ffc787aabbff6f7a"
  STARHUB_SERVER_CASDOOR_ENDPOINT: {{ include "casdoor.external.endpoint" . | quote }}
  STARHUB_SERVER_CASDOOR_ORGANIZATION_NAME: "OpenCSG"
  STARHUB_SERVER_CASDOOR_APPLICATION_NAME: "CSGHub"
  STARSHIP_SERVER_CASDOOR_CLIENT_ID: "922b009c161d8efb3422"
  STARSHIP_SERVER_CASDOOR_CLIENT_SECRET: "f55bd1917a52a24080f831a31010157e50f17f5a"
  STARSHIP_SERVER_CASDOOR_ORGANIZATION_NAME: "OpenCSG"
  STARSHIP_SERVER_CASDOOR_APPLICATION_NAME: "StarShip"
  app.conf: |
    appname = casdoor
    httpport = 8000
    runmode = prod
    copyrequestbody = true
    driverName = postgres
    dataSourceName = {{ include "casdoor.postgresql.dsn" . | quote }}
    dbName =
    tableNamePrefix =
    showSql = false
    redisEndpoint =
    defaultStorageProvider =
    isCloudIntranet = false
    authState = "casdoor"
    socks5Proxy = "127.0.0.1:10808"
    verificationCodeTimeout = 10
    initScore = 0
    logPostOnly = true
    origin = {{ include "casdoor.external.endpoint" . | quote }}
    originFrontend =
    staticBaseUrl = "https://cdn.casbin.org"
    isDemoMode = false
    batchSize = 100
    enableGzip = true
    ldapServerPort = 389
    radiusServerPort = 1812
    radiusSecret = "secret"
    quota = {"organization": -1, "user": -1, "application": -1, "provider": -1}
    logConfig = {"filename": "logs/casdoor.log", "maxdays":99999, "perm":"0770"}
    initDataFile = "/conf/init_data.json"
    initDataNewOnly = true
    frontendBaseDir = "../casdoor"
  init_data.json: |
    {
      "organizations": [
        {
          "owner": "admin",
          "name": "OpenCSG",
          "displayName": "OpenCSG",
          "websiteUrl": {{ include "csghub.external.endpoint" . | quote }},
          "favicon": "https://cdn.jsdelivr.net/gh/OpenCSGs/images/favicon.png",
          "logo": "https://cdn.jsdelivr.net/gh/OpenCSGs/images/logo.png",
          "passwordType": "bcrypt",
          "passwordSalt": "OpenCSG",
          "passwordOptions": [
            "AtLeast8",
            "Aa123",
            "SpecialChar"
          ],
          "countryCodes": [
            "CN",
            "US"
          ],
          "defaultAvatar": "https://cdn.jsdelivr.net/gh/OpenCSGs/images/robot.png",
          "defaultApplication": "CSGHub",
          "tags": [],
          "languages": [
            "zh",
            "en"
          ],
          "themeData": {
            "themeType": "default",
            "colorPrimary": "#1C8B7F",
            "borderRadius": 6,
            "isCompact": false,
            "isEnabled": true
          },
          "masterPassword": "",
          "defaultPassword": "",
          "initScore": 2000,
          "enableSoftDeletion": true,
          "isProfilePublic": true,
          "mfaItems": [],
          "useEmailAsUsername": false,
          "enableTour": false,
          "accountItems": [
            {
              "name": "Organization",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Admin",
              "regex": ""
            },
            {
              "name": "ID",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Immutable",
              "regex": ""
            },
            {
              "name": "Name",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Admin",
              "regex": ""
            },
            {
              "name": "Display name",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Self",
              "regex": ""
            },
            {
              "name": "Avatar",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Self",
              "regex": ""
            },
            {
              "name": "User type",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Admin",
              "regex": ""
            },
            {
              "name": "Password",
              "visible": true,
              "viewRule": "Self",
              "modifyRule": "Self",
              "regex": ""
            },
            {
              "name": "Email",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Self",
              "regex": ""
            },
            {
              "name": "Phone",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Self",
              "regex": ""
            },
            {
              "name": "Country/Region",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Self",
              "regex": ""
            },
            {
              "name": "Location",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Self",
              "regex": ""
            },
            {
              "name": "Address",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Self",
              "regex": ""
            },
            {
              "name": "Language",
              "visible": true,
              "viewRule": "Public",
              "modifyRule": "Admin",
              "regex": ""
            },
            {
              "name": "Is admin",
              "visible": true,
              "viewRule": "Admin",
              "modifyRule": "Admin",
              "regex": ""
            }
          ]
        }
      ],
      "applications": [
        {
          "owner": "admin",
          "name": "CSGHub",
          "displayName": "CSGHub",
          "logo": "https://cdn.jsdelivr.net/gh/OpenCSGs/images/logo.png",
          "homepageUrl": {{ include "csghub.external.endpoint" . | quote }},
          "organization": "OpenCSG",
          "cert": "csghub-cert",
          "enablePassword": true,
          "enableSignUp": true,
          "enableSigninSession": false,
          "enableAutoSignin": false,
          "enableCodeSignin": false,
          "enableSamlCompress": false,
          "enableSamlC14n10": false,
          "enableSamlPostBinding": false,
          "enableWebAuthn": false,
          "enableLinkWithEmail": true,
          "orgChoiceMode": "Select",
          "samlReplyUrl": "",
          "clientId": "7a97bc5168cb75ffc514",
          "clientSecret": "33bd85106818efd90c57fb35ffc787aabbff6f7a",
          "providers": [
            {
              "owner": "",
              "name": "Email_Provider",
              "canSignUp": true,
              "canSignIn": true,
              "canUnlink": true,
              "prompted": false,
              "rule": "all"
            },
            {
              "owner": "",
              "name": "Captcha_Provider",
              "canSignUp": true,
              "canSignIn": true,
              "canUnlink": true,
              "prompted": false,
              "rule": "None"
            }
          ],
          "signinMethods": [
            {
              "name": "Password",
              "displayName": "Password",
              "rule": "All"
            }
          ],
          "signupItems": [
            {
              "name": "ID",
              "visible": false,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "Random"
            },
            {
              "name": "Username",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            },
            {
              "name": "Display name",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            },
            {
              "name": "Password",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            },
            {
              "name": "Confirm password",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            },
            {
              "name": "Email",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "No verification"
            },
            {
              "name": "Agreement",
              "visible": false,
              "required": false,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            }
          ],
          "signinItems": [
            {
              "name": "Back button",
              "visible": false,
              "label": "\n\u003cstyle\u003e\n  .back-button {\n      top: 65px;\n      left: 15px;\n      position: absolute;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Languages",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-languages {\n      top: 55px;\n      right: 5px;\n      position: absolute;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Logo",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-logo-box {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Signin methods",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .signin-methods {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Username",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-username {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Password",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-password {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Agreement",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-agreement {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Forgot password?",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-forget-password {\n    display: inline-flex;\n    justify-content: space-between;\n    width: 320px;\n    margin-bottom: 25px;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Login button",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-button-box {\n    margin-bottom: 5px;\n  }\n  .login-button {\n    width: 100%;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Signup link",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-signup-link {\n    margin-bottom: 24px;\n    display: flex;\n    justify-content: end;\n}\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Providers",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .provider-img {\n      width: 30px;\n      margin: 5px;\n  }\n  .provider-big-img {\n      margin-bottom: 10px;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "small",
              "isCustom": false
            }
          ],
          "grantTypes": [
            "authorization_code",
            "id_token"
          ],
          "redirectUris": [
            "{{ include "csghub.external.endpoint" . }}/api/v1/callback/casdoor"
          ],
          "tokenFormat": "JWT",
          "tokenFields": [],
          "expireInHours": 168,
          "refreshExpireInHours": 168,
          "themeData": {
            "themeType": "lark",
            "colorPrimary": "#1C8B7F",
            "borderRadius": 6,
            "isCompact": false,
            "isEnabled": true
          },
          "footerHtml": "<C2><A9>2025 OpenCSG. All rights reserved.",
          "formCSS": "",
          "formOffset": 2,
          "formSideHtml": "<style>  .left-model{    text-align: center;    padding: 60px;    background-image: url('https://cdn.jsdelivr.net/gh/OpenCSGs/images/background.png');    background-size: cover;    background-position: center;    position: absolute;    transform: none;    width: 100%;    height: 100%;  }  .side-logo {    display: flex;    align-items: center;  }  .side-logo span {    font-family: Montserrat, sans-serif;    font-weight: 900;    font-size: 2.4rem;    line-height: 1.3;    margin-left: 16px;    color: #404040;  }  .img{    max-width: none;    margin: 41px 0 13px;  }</style><div class='left-model'>  <span class='side-logo'> <img src='' alt='' style='width: 120px'>   </span>  <div class='img'>    <img src='' alt='' style='width: 120px'/>  </div></div>",
          "failedSigninLimit": 5,
          "failedSigninFrozenTime": 15
        },
        {
          "owner": "admin",
          "name": "StarShip",
          "displayName": "StarShip",
          "logo": "https://cdn.jsdelivr.net/gh/OpenCSGs/images/logo.png",
          "homepageUrl": {{ include "starship.external.endpoint" . | quote }},
          "organization": "OpenCSG",
          "cert": "starship-cert",
          "enablePassword": true,
          "enableSignUp": true,
          "enableSigninSession": false,
          "enableAutoSignin": false,
          "enableCodeSignin": false,
          "enableSamlCompress": false,
          "enableSamlC14n10": false,
          "enableSamlPostBinding": false,
          "enableWebAuthn": false,
          "enableLinkWithEmail": true,
          "orgChoiceMode": "Select",
          "samlReplyUrl": "",
          "clientId": "922b009c161d8efb3422",
          "clientSecret": "f55bd1917a52a24080f831a31010157e50f17f5a",
          "providers": [
            {
              "owner": "",
              "name": "Email_Provider",
              "canSignUp": true,
              "canSignIn": true,
              "canUnlink": true,
              "prompted": false,
              "rule": "all"
            },
            {
              "owner": "",
              "name": "Captcha_Provider",
              "canSignUp": true,
              "canSignIn": true,
              "canUnlink": true,
              "prompted": false,
              "rule": "None"
            },
            {
              "owner": "",
              "name": "GitLab_Provider",
              "canSignUp": true,
              "canSignIn": true,
              "canUnlink": true,
              "countryCodes": null,
              "prompted": false,
              "signupGroup": "",
              "rule": "None",
              "provider": null
            }
          ],
          "signinMethods": [
            {
              "name": "Password",
              "displayName": "Password",
              "rule": "All"
            }
          ],
          "signupItems": [
            {
              "name": "ID",
              "visible": false,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "Random"
            },
            {
              "name": "Username",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            },
            {
              "name": "Display name",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            },
            {
              "name": "Password",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            },
            {
              "name": "Confirm password",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            },
            {
              "name": "Email",
              "visible": true,
              "required": true,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "No verification"
            },
            {
              "name": "Agreement",
              "visible": false,
              "required": false,
              "prompted": false,
              "label": "",
              "placeholder": "",
              "regex": "",
              "rule": "None"
            }
          ],
          "signinItems": [
            {
              "name": "Back button",
              "visible": false,
              "label": "\n\u003cstyle\u003e\n  .back-button {\n      top: 65px;\n      left: 15px;\n      position: absolute;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Languages",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-languages {\n      top: 55px;\n      right: 5px;\n      position: absolute;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Logo",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-logo-box {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Signin methods",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .signin-methods {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Username",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-username {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Password",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-password {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Agreement",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-agreement {\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Forgot password?",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-forget-password {\n    display: inline-flex;\n    justify-content: space-between;\n    width: 320px;\n    margin-bottom: 25px;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Login button",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-button-box {\n    margin-bottom: 5px;\n  }\n  .login-button {\n    width: 100%;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Signup link",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .login-signup-link {\n    margin-bottom: 24px;\n    display: flex;\n    justify-content: end;\n}\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "None",
              "isCustom": false
            },
            {
              "name": "Providers",
              "visible": true,
              "label": "\n\u003cstyle\u003e\n  .provider-img {\n      width: 30px;\n      margin: 5px;\n  }\n  .provider-big-img {\n      margin-bottom: 10px;\n  }\n\u003c/style\u003e\n",
              "placeholder": "",
              "rule": "small",
              "isCustom": false
            }
          ],
          "grantTypes": [
            "authorization_code",
            "id_token"
          ],
          "redirectUris": [
            "{{ include "starship.external.api.endpoint" . }}/api/v1/account/casdoor/login/callback"
          ],
          "tokenFormat": "JWT",
          "tokenFields": [],
          "expireInHours": 168,
          "refreshExpireInHours": 168,
          "themeData": {
            "themeType": "lark",
            "colorPrimary": "#1C8B7F",
            "borderRadius": 6,
            "isCompact": false,
            "isEnabled": true
          },
          "footerHtml": "<C2><A9>2025 OpenCSG. All rights reserved.",
          "formCSS": "",
          "formOffset": 2,
          "formSideHtml": "<style>  .left-model{    text-align: center;    padding: 60px;    background-image: url('https://cdn.jsdelivr.net/gh/OpenCSGs/images/background.png');    background-size: cover;    background-position: center;    position: absolute;    transform: none;    width: 100%;    height: 100%;  }  .side-logo {    display: flex;    align-items: center;  }  .side-logo span {    font-family: Montserrat, sans-serif;    font-weight: 900;    font-size: 2.4rem;    line-height: 1.3;    margin-left: 16px;    color: #404040;  }  .img{    max-width: none;    margin: 41px 0 13px;  }</style><div class='left-model'>  <span class='side-logo'> <img src='' alt='' style='width: 120px'>   </span>  <div class='img'>    <img src='' alt='' style='width: 120px'/>  </div></div>",
          "failedSigninLimit": 5,
          "failedSigninFrozenTime": 15
        }
      ],
      "certs": [
        {
          "owner": "admin",
          "name": "csghub-cert",
          "displayName": "CSGHub Cert",
          "scope": "JWT",
          "type": "x509",
          "cryptoAlgorithm": "RS256",
          "bitSize": 4096,
          "expireInYears": 20,
          "certificate": "-----BEGIN CERTIFICATE-----\nMIIE3TCCAsWgAwIBAgIDAeJAMA0GCSqGSIb3DQEBCwUAMCgxDjAMBgNVBAoTBWFk\nbWluMRYwFAYDVQQDEw1jZXJ0LWJ1aWx0LWluMB4XDTI0MDgxMzA2NTU1M1oXDTQ0\nMDgxMzA2NTU1M1owKDEOMAwGA1UEChMFYWRtaW4xFjAUBgNVBAMTDWNlcnQtYnVp\nbHQtaW4wggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCh08MiRaD//vmL\nFq/1IDcoQb7rpgkoIRX9fhlxJR0NIRtGiQ2Ugzuu4d3MldNSs0xpTetZu7XU4gmC\n3i8acr8bwvTu1QnhIn92/jkc7FMcbPO/WCdExuTHZhYlCQOiQ10WoxSVju6T1viZ\n52lNA07k7Ij6+QLyM7HK0OEzPC4m4BXdjaG2o8dv0b7o80vRdCtmhUsO9EE4QiY3\n1GFrseKGPfKWFAg6bf/5LwcuW64onW/dJjBg4E7m7tZlXnhPCUK0WTEhh5IrxgT7\nE2kV1Bf2lUgw4AiPmfZpCuIrxo2r8jKfRIMGyByq+EoG4X+8BBci7H13MkrQpktv\n9PmkAXtWy1D2ACFr8e8FM6JgCHVXXHBna6HJxsUXIEuaceLebrXH8be29UJcEHnw\ntQOGJRO8/2axWa6j6zVLGUAhYlVYHcg6NIkrV8ClHVYHSGsSsjRwvtiLY10t1PLh\nc7BeTXSbXLSS7FQIeY8JiJQNsfVhlDSMimup5gwYBZrqIW719ICC/ez3FYuv/ykE\nu19PZj86I8G6p/LMK3oVq+erH+da+uSpCzkfyS1YDcC19veYt4BOdFSZi4rFrQPQ\njh7YrceU8Fhf9A+lc1F/jYysi0uM44vKQR39EsuyPNynqBFEmC2Jj0HuH3LPxKJa\nYdqprTZPRaQ2zPi3FKBmiztLU+fSiQIDAQABoxAwDjAMBgNVHRMBAf8EAjAAMA0G\nCSqGSIb3DQEBCwUAA4ICAQAt8cwzXTLYJh2WkCjQNK35D+6fgV1EbJlyEnfPM9N/\nVla+CNPR9I7KRo1EVr+GmCNhR7ZjzkaCBAVIxx3nQVVejkrITjGEQQ+0nXV92dDA\njVNQ54JkhiWovlqysbqOiiXAXrrEfu1dIGg7rM7JSOMkd8BhtBolvanB5SpyHPXs\nZakS3Wsf6TAa4yy2MCVzTf20m/pMzK54ZjRPUQUUxFkqpF28BjOZcKRDT9rDwiq4\n6UolMnIT2Ept4n2M+IOkqHPtVVqioSFZlvMDv7cjyTHA0JKUYvdaUX5tCBf58xS/\nGwdE6KDLJsT/Vvy+xUbFOQDs+HNsPyC8eAyz/jhb1sKKaJG6SR48QW61nyAC7cuy\nHfknaNViOK8tFdeLM1UAmI+GEfJvaURcaVxaAmpy7HPZKUYIhrXBYH0f/tQUHkhX\n15tCN5BtRco8lhUhFtQ0kUF4R/rQOwpJ9nbqab8yCwNKOzTL0RBVkPwlGJW64Pi3\n1eZSf8M2eyjY9IZhSk3tChWYcqZTQeYLkRo1GTHRY/v8h2nwsTz0Q/sSSIMCrhSK\nZRsf+ElDrcf0wIyYZMPWZjbNgH2+cubf3wrJw2aWqV29pAw4gUSQw5xD4iqOGm3N\n0GeYNWmio297Nm4bcYvf3CaqQC82XZiu5PTQ/jtgg82dWFfI7ZSisAPr775cNvn+\nXw==\n-----END CERTIFICATE-----\n",
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        },
        {
          "owner": "admin",
          "name": "starship-cert",
          "displayName": "StarShip Cert",
          "scope": "JWT",
          "type": "x509",
          "cryptoAlgorithm": "RS256",
          "bitSize": 4096,
          "expireInYears": 20,
          "certificate": "-----BEGIN CERTIFICATE-----\nMIIE2TCCAsGgAwIBAgIDAeJAMA0GCSqGSIb3DQEBCwUAMCYxDjAMBgNVBAoTBWFk\nbWluMRQwEgYDVQQDDAtjZXJ0X3Vpd3RhaDAeFw0yNTA0MjIwMjQ1NDNaFw00NTA0\nMjIwMjQ1NDNaMCYxDjAMBgNVBAoTBWFkbWluMRQwEgYDVQQDDAtjZXJ0X3Vpd3Rh\naDCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMbtx+W+pW5PbB99BbQt\nWX0laH9OQ72Hz9P19bDW9L96UlXBniPdePcJKFb6bbJ+TL1GdTOE/bjW71QGc8P3\nwNgqsLk01mrkgh0okbov6UR+C4hOVP+t9S6DIWN0etzyODTlpkWEd9/FU2gVhIRb\nxaHNX0qQXTotzAjs80AFuZnqG3jpF/7SOByE7fFNHXBB7UTPSjVm3EJX3jsGNS6D\n7YtXBZgYDb0Y85pBHAZxbaVp2W49bMBWaUCKLDOiTjKmipJ96N0FKbB5Q0D+vv2e\nNlpdaUQXY9sHtYt8j+Pk4x+61OKfSKYF1nWqO3u33qaMNGiV/L37IBXYeAmceKt6\nVhMG2l50+nG6jVnsLM0j4YnXR3Ry5+K4R8FXeBjcF/fXxbHFulfWUjZg8gz6mPeZ\n0WCMgAXPy7Lk922EMyFV3z6kceO01IbXZj/TSQRfvI7jKrmgO15TGyKJZTgfId3c\nSfCrSDfr2ITu/vPr4AHzF394Mzhy6s35NziYUG49NDBtFigLumQtZoa83h+TX+rD\n4YrBmi5ctFwtcdXfPqtXuVGuqe2X3r+qqk9hUbX0gL000wqD9ZPFNXTAPkAn1J1f\nljqv7Q8fwnmtcBeMMCHcJ6JTt4j5RwPhy4rowdU54pY3xqVLUYvrYXgvDT8Xky7t\nOhaAeL2HIM6rD+wSXKqa9Wp3AgMBAAGjEDAOMAwGA1UdEwEB/wQCMAAwDQYJKoZI\nhvcNAQELBQADggIBAKlrL66aClTcWGvBu/oRTMYRqhUPSNZZ+scoRM0xI/msc8o/\nb4m09kb7O03kgH3TU5elBqEDXPuMqo9JZJkTQoCVSWKEGoWDmz//EH8FyHrM25jr\naOLJE2klHlYTEfYqem4T1TIbyb5Ho5L+ikRdhux+IXb0PrGD9SqRTMfPUnNEs8ws\nSOty+HCStAmEvAI3JRfuu0mEsDGkeiRdq1jQrQs72elVXNAa4ffD3vJOuXQJFTiI\nreEgczTxUgQ2vE0u77/2hB7UPfDGJGx3bOFtQjn59gZ21C6/e4t1DXqnDLaU/so+\n4VuankSYVtTQ+a5Av4c0RP4DJlNpUx4kOEQyJYfSeJ7P8qticHzVVHeHDGl03nMZ\n5pdYrnKnXZOSgRLYp9hANGVdvzx6SMtx0naunUBjdsaKiaCt6q8aHCCCdRsS46gh\nOhDx2dIqHvqHeaiksAHM1+L6gSw7GJ6xFNslG3bAk54vCiDbhYTvmLqyB0ZCXfND\nxueoD47543zpAUfnpn2Dl35vEkV5COh7XIoj766rpHCSef0RBTS/atZn3umjJYoG\ndS+cCRxtXll9qmvND32nl9nE7MIAUndu7daF1Tzd6f11XukRBl34nwxx9/JWfHB3\nLEq+6UmBri7mQdOZd/mwbSAkTlp8J1b4RZ1h+pr1EgaJ0R4YAtYJ6NX7OUnS\n-----END CERTIFICATE-----\n",
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        }
      ],
      "users": [
        {
          "owner": "OpenCSG",
          "name": "root",
          "password": {{ $htpasswd | quote }},
          "passwordSalt": "OpenCSG",
          "passwordType": "bcrypt",
          "displayName": "root",
          "email": "<EMAIL>",
          "type": "normal-user",
          "avatar": "https://cdn.jsdelivr.net/gh/OpenCSGs/images/robot.png",
          "isAdmin": true,
          "signupApplication": "CSGHub"
        }
      ],
      "providers": [
        {
          "owner": "admin",
          "name": "Captcha_Provider",
          "displayName": "Captcha Provider",
          "category": "Captcha",
          "type": "Default"
        },
        {
          "owner": "admin",
          "name": "Email_Provider",
          "displayName": "Email Provider",
          "category": "Email",
          "type": "Default",
          "method": "POST",
          "clientId": "",
          "clientSecret": "",
          "clientId2": "",
          "clientSecret2": "",
          "userMapping":
            {
              "avatarUrl": "avatarUrl",
              "displayName": "displayName",
              "email": "email",
              "id": "id",
              "username": "username"
            },
          "host": "",
          "port": 465,
          "disableSsl": false,
          "title": "OpenCSG Verification Code",
          "content": "\u003c!DOCTYPE html\u003e\n\u003chtml lang=\u0022en\u0022\u003e\n\u003chead\u003e\n\u003cmeta charset=\u0022UTF-8\u0022\u003e\n\u003cmeta name=\u0022viewport\u0022 content=\u0022width=device-width, initial-scale=1.0\u0022\u003e\n\u003ctitle\u003eVerification Code Email\u003c/title\u003e\n\u003cstyle\u003ebody { font-family: Arial, sans-serif; } .email-container { width: 600px; margin: 0 auto; } .header { text-align: center; } .code { font-size: 24px; margin: 20px 0; text-align: center; } .footer { font-size: 12px; text-align: center; margin-top: 50px; } .footer a { color: #000; text-decoration: none; } .center { text-align: center; } .center a { text-decoration: none; color: #1C8B7F; }\u003c/style\u003e\n\u003c/head\u003e\n\u003cbody\u003e\n\u003cdiv class=\u0022email-container\u0022\u003e\n  \u003cdiv class=\u0022header\u0022\u003e\n    \u003cimg src=\u0022https://cdn.jsdelivr.net/gh/OpenCSGs/images/logo.png\u0022 alt=\u0022OpenCSG Logo\u0022 width=\u0022300\u0022\u003e\n  \u003c/div\u003e\n    \u003cp\u003e\u003cstrong\u003e%{user.friendlyName}\u003c/strong\u003e, here is your verification code\u003c/p\u003e\n    \u003cp\u003eUse this code for your transaction. It's valid for 5 minutes\u003c/p\u003e\n    \u003cdiv class=\u0022code\u0022\u003e\n        %s\n    \u003c/div\u003e\n  \u003chr\u003e\n  \u003cp class=\u0022center\u0022\u003e\n    \u003ca href=\u0022https://opencsg.com\u0022 target=\u0022_blank\u0022\u003eOpenCSG Team\u003c/a\u003e\n  \u003c/p\u003e\n\u003c/div\u003e\n\u003c/body\u003e\n\u003c/html\u003e",
          "enableSignAuthnRequest": false
        },
        {
          "owner": "admin",
          "name": "GitLab_Provider",
          "displayName": "GitLab Provider",
          "category": "OAuth",
          "type": "Custom",
          "method": "Normal",
          "clientId": {{ .Values.global.starship.oauth.clientId | default "" | quote }},
          "clientSecret": {{ .Values.global.starship.oauth.clientSecret | default "" | quote }},
          "customAuthUrl": {{ printf "%s/oauth/authorize" .Values.global.starship.oauth.issuer | default "" | quote }},
          "customTokenUrl": {{ printf "%s/oauth/token" .Values.global.starship.oauth.issuer | default "" | quote }},
          "customUserInfoUrl": {{ printf "%s/api/v4/user" .Values.global.starship.oauth.issuer | default "" | quote }},
          "customLogo": "https://cdn.jsdelivr.net/gh/OpenCSGs/images/gitlab_favicon.png",
          "scopes": "read_user openid profile email",
          "userMapping":
          {
            "avatarUrl": "avatar_url",
            "displayName": "name",
            "email": "email",
            "id": "id",
            "username": "username"
          },
          "issuerUrl": {{ .Values.global.starship.oauth.issuer | default "" | quote }},
          "providerUrl": {{ .Values.global.starship.oauth.issuer | default "" | quote }}
        }
      ]
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" (list . "casdoor-init") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  update_casdoor.sql: |
    --
    -- Record Timestamp
    --
    SELECT now() as "Execute Timestamp";

    --
    -- PostgreSQL database dump
    --
    SET exit_on_error = on;
    SET statement_timeout = 0;
    SET lock_timeout = 0;
    SET idle_in_transaction_session_timeout = 0;
    SET client_encoding = 'UTF8';
    SET standard_conforming_strings = on;
    SET check_function_bodies = false;
    SET xmloption = content;
    SET client_min_messages = warning;
    SET row_security = off;

    --
    -- Set Default Schema for All Tables
    --

    SELECT pg_catalog.set_config('search_path', 'public', false);

    --
    -- Name: application; Type: TABLE; Schema: public; Owner: csghub
    --
    -- Connect to casdoor management database

    -- Update RedirectURLs
    UPDATE
        application
    SET
        redirect_uris = '["{{ include "csghub.external.endpoint" . }}/api/v1/callback/casdoor"]'
    WHERE
        name = 'CSGHub';

    -- Enable org Select
    UPDATE
        application
    SET
        enable_sign_up = 'f',
        org_choice_mode = 'Select'
    WHERE
        name = 'app-built-in';

    -- Reset admin user default password
    UPDATE
        "user"
    SET
        password_type = 'bcrypt',
        password = {{ $htpasswd | squote }}
    WHERE
        name = 'admin'
        AND password_type = 'plain';

    {{- if and (hasKey .Values.global.starship "oauth")
            (hasKey .Values.global.starship.oauth "clientId")
            (hasKey .Values.global.starship.oauth "clientSecret")
            (hasKey .Values.global.starship.oauth "issuer") }}
    -- Update GitLab provider
    UPDATE
        provider
    SET
        client_id = {{ .Values.global.starship.oauth.clientId | squote }},
        client_secret = {{ .Values.global.starship.oauth.clientSecret | squote }},
        custom_auth_url = {{ printf "%s/oauth/authorize" .Values.global.starship.oauth.issuer | squote }},
        custom_token_url = {{ printf "%s/oauth/token" .Values.global.starship.oauth.issuer | squote }},
        custom_user_info_url = {{ printf "%s/api/v4/user" .Values.global.starship.oauth.issuer | squote }}
    WHERE
        name = 'GitLab_Provider';
    {{- end }}
{{- end }}
