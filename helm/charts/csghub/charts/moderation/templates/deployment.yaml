{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.global.moderation.enabled }}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  {{- with (include "common.annotations.deployment" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      {{- with (include "common.annotations.pod.checksum" (dict "context" . "configmap" true "secret" false)) }}
      annotations:
        {{- . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- with (or .Values.global.image.pullSecrets .Values.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
      {{- end }}
      initContainers:
        - name: wait-for-server
          image: {{ or .Values.global.image.registry .Values.image.registry }}/busybox:latest
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until nc -z {{ include "server.internal.domain" . }} {{ include "server.internal.port" . }}; do echo 'Wait for csghub-server to be ready'; sleep 2; done" ]
      containers:
        - name: moderation
          image: {{ or .Values.global.image.registry .Values.image.registry }}/{{ or .Values.global.image.name .Values.image.repository }}:{{ include "csghub.image.tag" (dict "tag" (or .Values.global.image.tag .Values.image.tag) "context" .) }}
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.image.pullPolicy }}
          command: [ "/starhub-bin/starhub", "moderation", "launch" ]
          ports:
            - containerPort: 8089
              name: moderation
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" . }}
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
          {{- with .Values.environments }}
          env:
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          resources:
            {{- .Values.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: 8089
            initialDelaySeconds: 20
            periodSeconds: 10
          securityContext:
            {{- .Values.podSecurityContext | toYaml | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
