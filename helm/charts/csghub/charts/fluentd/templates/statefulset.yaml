{{- if eq .Values.kind "StatefulSet" }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "fluentd.fullname" . }}
  labels:
    {{- include "fluentd.labels" . | nindent 4 }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  serviceName: {{ include "fluentd.fullname" . }}
  {{- with .Values.updateStrategy }}
  updateStrategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "fluentd.selectorLabels" . | nindent 6 }}
  {{- with .Values.minReadySeconds }}
  minReadySeconds: {{ . }}
  {{- end }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/fluentd-configurations-cm.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "fluentd.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- include "fluentd.pod" . | nindent 6 }}
  {{- if or .Values.persistence.enabled }}
  volumeClaimTemplates:
    {{- if or .Values.persistence.enabled }}
    - metadata:
        name: {{ include "fluentd.fullname" . }}-buffer
      spec:
        accessModes: {{ or .Values.global.persistence.accessMode .Values.persistence.accessMode }}
        resources:
          requests:
            storage: {{ .Values.persistence.size }}
        storageClassName: {{ or .Values.global.persistence.storageClass .Values.persistence.storageClass }}
    {{- end }}
  {{- end }}
{{- end }}
