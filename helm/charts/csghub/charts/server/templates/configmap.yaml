{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    resource.dependencies/deployments: |
      {{ include "common.names.custom" . }}
      {{ include "common.names.custom" (list . "user") }}
      {{ include "common.names.custom" (list . "accounting") }}
      {{ include "common.names.custom" (list . "casdoor") }}
      {{ include "common.names.custom" (list . "mirror") }}
      {{ include "common.names.custom" (list . "portal") }}
      {{ include "common.names.custom" (list . "proxy") }}
      {{ include "common.names.custom" (list . "runner") }}
      {{ include "common.names.custom" (list . "dataviewer") }}
      {{ include "common.names.custom" (list . "moderation") }}
data:
  GIN_MODE: release
  STARHUB_SERVER_SAAS: "false"
  STARHUB_SERVER_MIRRORSERVER_ENABLE: "false"
  STARHUB_SERVER_MIRRORSERVER_HOST: ""
  {{- $token := include "server.hub.api.token" . }}
  {{- $tokenConfigMap := include "common.names.custom" . -}}
  {{- $tokenConfigMapData := (lookup "v1" "ConfigMap" .Release.Namespace $tokenConfigMap).data }}
  {{- if $tokenConfigMapData }}
  {{- $tokenFromConfigMap := index $tokenConfigMapData "STARHUB_SERVER_API_TOKEN" }}
  {{- if eq (len $tokenFromConfigMap) 128 }}
  {{- $token = $tokenFromConfigMap }}
  {{- end }}
  {{- end }}
  STARHUB_SERVER_API_TOKEN: {{ $token | quote }}
  STARHUB_SERVER_MIRROR_REMOTE: "false"
  STARHUB_SERVER_MODEL_DOWNLOAD_ENDPOINT: {{ include "csghub.external.endpoint" . }}
  STARHUB_JWT_SIGNING_KEY: "e2kk6awudc3620ed9a"

  # Multiple-Sync Enabled
  STARHUB_SERVER_MULTI_SYNC_ENABLED: "true"
  # Multiple-Sync fetch timeout
  STARHUB_SERVER_GIT_OPERATION_TIMEOUT: "120"

  # Redis Connection Info
  STARHUB_SERVER_REDIS_ENDPOINT: {{ include "csghub.redis.endpoint" . }}
  {{- if .Values.global.redis.external }}
  STARHUB_SERVER_REDIS_PASSWORD: {{ include "csghub.redis.password" . }}
  {{- end }}

  # PostgreSQL Connection Info
  STARHUB_DATABASE_HOST: {{ include "csghub.postgresql.host" . }}
  STARHUB_DATABASE_PORT: {{ include "csghub.postgresql.port" . | quote }}
  {{- $user := include "csghub.postgresql.user" . }}
  {{- $password := include "postgresql.initPass" $user }}
  {{- $secret := (include "common.names.custom" (list . "postgresql")) -}}
  {{- $secretData := (lookup "v1" "Secret" .Release.Namespace $secret).data }}
  {{- if $secretData }}
  {{- $secretPassword := index $secretData $user }}
  {{- if $secretPassword }}
  {{- $password = $secretPassword | b64dec }}
  {{- end }}
  {{- end }}
  {{- $password = or (include "csghub.postgresql.password" .) $password }}
  STARHUB_DATABASE_USERNAME: {{ $user }}
  STARHUB_DATABASE_PASSWORD: {{ $password }}
  STARHUB_DATABASE_NAME: {{ include "csghub.postgresql.database" . }}
  STARHUB_DATABASE_TIMEZONE: {{ include "csghub.postgresql.timezone" . }}
  STARHUB_DATABASE_DSN: {{ printf "postgresql://%s:%s@%s:%s/%s?sslmode=disable" $user $password (include "csghub.postgresql.host" .) (include "csghub.postgresql.port" .) (include "csghub.postgresql.database" .) }}

  # Object Storage Connection Info
  STARHUB_SERVER_S3_ENDPOINT: {{ include "csghub.objectStore.endpoint" . | trimPrefix "http://" | trimPrefix "https://" }}
  {{- if .Values.global.objectStore.external }}
  STARHUB_SERVER_S3_ACCESS_KEY_ID: {{ include "csghub.objectStore.accessKey" . }}
  STARHUB_SERVER_S3_ACCESS_KEY_SECRET: {{ include "csghub.objectStore.accessSecret" . }}
  {{- end }}
  STARHUB_SERVER_S3_BUCKET: {{ include "csghub.objectStore.bucket" . }}
  STARHUB_SERVER_S3_REGION: {{ include "csghub.objectStore.region" . }}
  STARHUB_SERVER_S3_ENABLE_SSL: {{ include "csghub.objectStore.encrypt" . | quote }}
  {{- if eq (include "csghub.objectStore.pathStyle" .) "true" }}
  STARHUB_SERVER_S3_BUCKET_LOOKUP: "path"
  {{- else }}
  STARHUB_SERVER_S3_BUCKET_LOOKUP: "auto"
  {{- end }}
  STARHUB_SERVER_SKIP_LFS_FILE_VALIDATION: {{ .Values.objectStore.directUpload | quote }}

  # Gitaly Connection Info
  STARHUB_SERVER_GITSERVER_TYPE: "gitaly"
  STARHUB_SERVER_GITALY_SERVER_SOCKET: {{ include "csghub.gitaly.endpoint" . }}
  STARHUB_SERVER_GITALY_TOKEN: {{ include "csghub.gitaly.token" . }}
  STARHUB_SERVER_GITALY_STORAGE: {{ include "csghub.gitaly.storage" . }}
  {{- if eq (include "csghub.gitaly.cluster" .) "true" }}
  STARHUB_SERVER_CHECK_FILE_SIZE_ENABLED: "false"
  {{- else }}
  STARHUB_SERVER_CHECK_FILE_SIZE_ENABLED: "true"
  {{- end }}
  {{- if eq (include "gitlab-shell.external.port" .) "22" }}
  STARHUB_SERVER_SSH_DOMAIN: {{ printf "ssh://git@%s" (include "csghub.external.domain" .)}}
  {{- else }}
  STARHUB_SERVER_SSH_DOMAIN: {{ printf "ssh://git@%s:%s" (include "csghub.external.domain" .) (include "gitlab-shell.external.port" .) }}
  {{- end }}

  # Accounting
  OPENCSG_ACCOUNTING_SERVER_HOST: {{ printf "http://%s" (include "accounting.internal.domain" .) }}
  OPENCSG_ACCOUNTING_SERVER_PORT: {{ include "accounting.internal.port" . | quote }}

  # User
  OPENCSG_USER_SERVER_HOST: {{ printf "http://%s" (include "user.internal.domain" .) }}
  OPENCSG_USER_SERVER_PORT: {{ include "user.internal.port" . | quote }}

  # Space Application
  STARHUB_SERVER_SPACE_BUILDER_ENDPOINT: {{ include "runner.internal.endpoint" . }}
  STARHUB_SERVER_SPACE_RUNNER_ENDPOINT: {{ include "runner.internal.endpoint" . }}
  STARHUB_SERVER_PUBLIC_DOMAIN: {{ include "csghub.external.endpoint" . }}

  # Deprecated ==> re-enabled
  {{- if .Values.global.deploy.usePublicDomain }}
  STARHUB_SERVER_PUBLIC_ROOT_DOMAIN: {{ include "csghub.external.public.endpoint" . | trimPrefix "http://" | trimPrefix "https://" | quote }}
  {{- else }}
  STARHUB_SERVER_PUBLIC_ROOT_DOMAIN: ""
  {{- end }}

  # Deprecated
  STARHUB_SERVER_INTERNAL_ROOT_DOMAIN: {{ printf "%s.app.internal:%s" .Values.global.deploy.namespace (include "proxy.internal.port" .) }}

  # Casdoor
  STARHUB_SERVER_CASDOOR_CERTIFICATE: "/starhub-bin/casdoor/tls.crt"

  # Workflow
  OPENCSG_WORKFLOW_SERVER_ENDPOINT: {{ include "temporal.internal.endpoint" . }}

  # Dataflow
  {{- if .Values.global.dataflow.enabled }}
  OPENCSG_DATAFLOW_SERVER_HOST: {{ printf "http://%s" (include "dataflow.internal.domain" .) }}
  OPENCSG_DATAFLOW_SERVER_PORT: {{ include "dataflow.internal.port" . | quote }}
  {{- end }}

  # Dataviewer
  OPENCSG_DATAVIEWER_SERVER_HOST: {{ printf "http://%s" (include "dataviewer.internal.domain" .) }}
  OPENCSG_DATAVIEWER_SERVER_PORT: {{ include "dataviewer.internal.port" . | quote }}

  # Moderation
  {{- if .Values.global.moderation.enabled }}
  STARHUB_SERVER_SENSITIVE_CHECK_ENABLE: "true"
  OPENCSG_MODERATION_SERVER_HOST: {{ printf "http://%s" (include "moderation.internal.domain" .)}}
  OPENCSG_MODERATION_SERVER_PORT: {{ include "moderation.internal.port" . | quote }}
  {{- else }}
  STARHUB_SERVER_SENSITIVE_CHECK_ENABLE: "false"
  {{- end }}

  STARHUB_SERVER_CRON_JOB_SYNC_AS_CLIENT_CRON_EXPRESSION: '*/5 * * * *'

  OPENCSG_TRACING_OTLP_LOGGING: "false"

  STARHUB_SERVER_SPACE_PYPI_INDEX_URL: {{ .Values.global.deploy.pipIndexUrl | quote}}

  # Notification
  STARHUB_SERVER_NOTIFIER_HOST: {{ printf "http://%s" (include "notification.internal.domain" .) }}
  STARHUB_SERVER_NOTIFIER_PORT: {{ include "notification.internal.port" . | quote }}
  # Notification extra config
  # STARHUB_SERVER_FEISHU_APP_ID: xxxxx
  # STARHUB_SERVER_FEISHU_APP_SECRET: xxxxx
  {{ include "notification.extraConfig" . | nindent 2 }}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" (list . "server-init") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    resource.dependencies/deployments: |
      {{ include "common.names.custom" . }}
      {{ include "common.names.custom" (list . "user") }}
      {{ include "common.names.custom" (list . "accounting") }}
      {{ include "common.names.custom" (list . "casdoor") }}
      {{ include "common.names.custom" (list . "mirror") }}
      {{ include "common.names.custom" (list . "portal") }}
      {{ include "common.names.custom" (list . "proxy") }}
      {{ include "common.names.custom" (list . "runner") }}
data:
  {{- $currentScope := . }}
  {{- range $path, $_ := .Files.Glob "scripts/*.sql" }}
    {{- with $currentScope }}
    {{- base $path | nindent 2 }}: |
      {{- .Files.Get $path | nindent 4 }}
    {{- end }}
  {{- end }}
