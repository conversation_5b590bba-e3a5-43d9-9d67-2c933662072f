[unix_http_server]
file=/var/run/supervisord.sock
username=root
password=root

[supervisord]
user=root
logfile=/var/log/supervisord.log
pidfile=/var/run/supervisord.pid
logfile_maxbytes=50MB
logfile_backups=5
loglevel=info
nodaemon=true

[supervisorctl]
serverurl=unix:///var/run/supervisord.sock
username=root
password=root
prompt=csghub

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:csghub-logger]
command=/usr/bin/logger
user=root
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0

[program:dnsmasq]
command=/scripts/dnsmasq-process-wrapper
user=root
startsecs=10
stopasgroup=true
killasgroup=true
redirect_stderr=true
stdout_logfile=/var/log/dnsmasq/dnsmasq.log
stdout_logfile_maxbytes=50MB

[program:postgresql]
command=/scripts/postgres-process-wrapper
user=postgres
startsecs=10
stopasgroup=true
killasgroup=true
redirect_stderr=true
stdout_logfile=/var/log/postgresql/postgresql.log
stdout_logfile_maxbytes=100MB

[program:postgres-init-job]
command=/scripts/postgres-init-job.sh
user=postgres
startsecs=0
autorestart=unexpected
startretries=3
exitcodes=0
redirect_stderr=true
stdout_logfile=/var/log/postgresql/postgres-init-job.log
stdout_logfile_maxbytes=1MB

[program:redis]
command=/scripts/redis-process-wrapper
user=redis
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/redis/redis.log
stdout_logfile_maxbytes=100MB

[program:registry]
command=/scripts/registry-process-wrapper
user=registry
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/registry/registry.log
stdout_logfile_maxbytes=100MB

[program:minio]
command=/scripts/minio-process-wrapper
user=minio
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/minio/minio.log
stdout_logfile_maxbytes=100MB

[program:minio-init-job]
command=/scripts/minio-init-job.sh
user=minio
startsecs=0
autorestart=unexpected
startretries=3
exitcodes=0
redirect_stderr=true
stdout_logfile=/var/log/minio/minio-init-job.log
stdout_logfile_maxbytes=1MB

[program:gitaly]
command=/scripts/gitaly-process-wrapper
user=git
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/gitaly/gitaly.log
stdout_logfile_maxbytes=100MB

[program:gitlab-shell]
command=/scripts/gitlab-shell-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/gitlab-shell/gitlab-shell.log
stdout_logfile_maxbytes=100MB

[program:nats]
command=/scripts/nats-process-wrapper
user=nats
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/nats/nats.log
stdout_logfile_maxbytes=100MB

[program:csghub-builder]
command=/scripts/csghub-builder-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-builder/csghub-builder.log
stdout_logfile_maxbytes=100MB

[program:casdoor]
command=/scripts/casdoor-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/casdoor/casdoor.log
stdout_logfile_maxbytes=100MB

[program:temporal]
command=/scripts/temporal-process-wrapper autosetup
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/temporal/temporal.log
stdout_logfile_maxbytes=100MB

[program:temporal-ui]
command=/scripts/temporal-ui-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/temporal/temporal-ui.log
stdout_logfile_maxbytes=100MB

[program:csghub-server]
command=/scripts/csghub-server-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-server/csghub-server.log
stdout_logfile_maxbytes=100MB

[program:csghub-user]
command=/scripts/csghub-user-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-user/csghub-user.log
stdout_logfile_maxbytes=100MB

[program:csghub-proxy]
command=/scripts/csghub-proxy-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-proxy/csghub-proxy.log
stdout_logfile_maxbytes=100MB

[program:csghub-accounting]
command=/scripts/csghub-accounting-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-accounting/csghub-accounting.log
stdout_logfile_maxbytes=100MB

[program:csghub-runner]
command=/scripts/csghub-runner-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-runner/csghub-runner.log
stdout_logfile_maxbytes=100MB

[program:mirror-repo]
command=/scripts/mirror-repo-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-server/mirror-repo.log
stdout_logfile_maxbytes=100MB

[program:seed-init-job]
command=/scripts/seed-init-job.sh
user=root
startsecs=0
autorestart=unexpected
startretries=3
exitcodes=0
redirect_stderr=true
stdout_logfile=/var/log/csghub-server/seed-init-job.log
stdout_logfile_maxbytes=100MB

[program:mirror-lfs]
command=/scripts/mirror-lfs-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-server/mirror-lfs.log
stdout_logfile_maxbytes=100MB

[program:mirror-init-job]
command=/scripts/mirror-init-job.sh
user=root
startsecs=0
autorestart=unexpected
startretries=3
exitcodes=0
redirect_stderr=true
stdout_logfile=/var/log/csghub-server/mirror-init-job.log
stdout_logfile_maxbytes=100MB

[program:csghub-portal]
command=/scripts/csghub-portal-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/csghub-portal/csghub-portal.log
stdout_logfile_maxbytes=100MB

[program:nginx]
command=/scripts/nginx-process-wrapper
user=root
startsecs=10
redirect_stderr=true
stdout_logfile=/var/log/nginx/nginx.log
stdout_logfile_maxbytes=100MB

[program:k8s-init-job]
command=/scripts/k8s-init-job.sh
user=root
startsecs=0
autorestart=unexpected
startretries=3
exitcodes=0
redirect_stderr=true
stdout_logfile=/var/log/csghub-runner/k8s-init-job.log
stdout_logfile_maxbytes=100MB