user root;
# May be equal to `grep processor /proc/cpuinfo | wc -l`
worker_processes auto;
worker_cpu_affinity auto;

# PCRE JIT can speed up processing of regular expressions significantly.
pcre_jit on;

load_module modules/ngx_stream_module.so;
daemon off;

# error_log
error_log /dev/stderr notice;

events {
    # Should be equal to `ulimit -n`
    worker_connections 1024;

    # Let each process accept multiple connections.
    multi_accept on;

    # Preferred connection method for newer linux versions.
    use epoll;
}

stream {
    server {
        listen ${GITLAB_SHELL_SSH_PORT};
        proxy_pass 127.0.0.1:22;
    }
}
http {
    # Disables the “Server” response header
    server_tokens off;
    charset utf-8;

    # Sendfile copies data between one FD and other from within the kernel.
    # More efficient than read() + write(), since the requires transferring
    # data to and from the user space.
    sendfile on;

    # Tcp_nopush causes nginx to attempt to send its HTTP response head in one
    # packet, instead of using partial frames. This is useful for prepending
    # headers before calling sendfile, or for throughput optimization.
    tcp_nopush on;

    # Don't buffer data-sends (disable Nagle algorithm). Good for sending
    # frequent small bursts of data in real time.
    #
    tcp_nodelay on;

    # http://nginx.org/en/docs/hash.html
    types_hash_max_size 4096;
    include mime.types;
    default_type application/octet-stream;

    log_format  main  '${DOLLAR}remote_addr - ${DOLLAR}remote_user [${DOLLAR}time_local] "${DOLLAR}request" '
                      '${DOLLAR}status ${DOLLAR}body_bytes_sent "${DOLLAR}http_referer" '
                      '"${DOLLAR}http_user_agent" "${DOLLAR}http_x_forwarded_for"';

    # Logging Settings
    access_log /dev/stdout main;

    # Gzip Settings
    gzip on;
    gzip_disable "msie6";

    gzip_comp_level 6;
    # gzip_comp_level 9;
    gzip_min_length 1100;
    gzip_buffers 16 8k; gzip_proxied any;
    # gzip_http_version 1.1;
    gzip_types text/plain application/xml text/css text/js text/xml application/x-javascript text/javascript application/json application/xml+rss;
    proxy_request_buffering off;
    proxy_buffering off;
    client_body_temp_path /var/nginx/client_body_temp;
    proxy_max_temp_file_size 150000M;
    client_max_body_size 150000M;
    client_body_timeout 300s;
    client_header_timeout 300s;
    send_timeout 300s;
    keepalive_timeout 300s;
    proxy_read_timeout      3600;
    proxy_connect_timeout   300;
    proxy_redirect          off;
    proxy_http_version 1.1;

    map ${DOLLAR}http_upgrade ${DOLLAR}connection_upgrade {
      default upgrade;
      ''      close;
    }

    server {
        listen 80;
        server_name ${SERVER_DOMAIN};

        location / {
            proxy_pass http://127.0.0.1:8090;
            proxy_set_header X-Forwarded-Proto ${DOLLAR}scheme;
            proxy_set_header X-Forwarded-Host ${DOLLAR}server_name:80;
            proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
            proxy_set_header Host ${DOLLAR}host;
        }

        location /api/ {
            proxy_pass http://127.0.0.1:8080/api/;
            proxy_set_header Host ${DOLLAR}host;
            proxy_set_header X-Real-IP ${DOLLAR}remote_addr;
            proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host ${DOLLAR}server_name;
            proxy_set_header X-Forwarded-Proto ${DOLLAR}scheme;
        }

        location /hf/ {
            proxy_pass http://127.0.0.1:8080/hf/;
            proxy_set_header Host ${DOLLAR}host;
            proxy_set_header X-Real-IP ${DOLLAR}remote_addr;
            proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host ${DOLLAR}server_name;
            proxy_set_header X-Forwarded-Proto ${DOLLAR}scheme;
        }

        location /temporal-ui/ {
            auth_basic "Please login with your account:";
            auth_basic_user_file /etc/nginx/.htpasswd;

            proxy_pass http://127.0.0.1:8180;
            proxy_set_header Host ${DOLLAR}host;
            proxy_set_header X-Real-IP ${DOLLAR}remote_addr;
            proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host ${DOLLAR}server_name;
            proxy_set_header X-Forwarded-Proto ${DOLLAR}scheme;
       }

        location /endpoint/ {
            proxy_pass http://127.0.0.1:8083;
            proxy_set_header Host ${DOLLAR}host;
            proxy_set_header X-Real-IP ${DOLLAR}remote_addr;
            proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host ${DOLLAR}server_name;
            proxy_set_header X-Forwarded-Proto ${DOLLAR}scheme;
            proxy_set_header Upgrade ${DOLLAR}http_upgrade;
            proxy_set_header Connection ${DOLLAR}connection_upgrade;
            proxy_cookie_flags ~ nosecure samesite=lax;
        }

        # used for git operations
        location ~* \.git(/.*)?${DOLLAR} {
            proxy_pass http://127.0.0.1:8080;
            proxy_set_header Host ${DOLLAR}host;
            proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto http;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root  /usr/share/nginx/html;
        }
    }

    server {
        listen 8000;
        server_name ${SERVER_DOMAIN};

        location / {
            proxy_pass http://127.0.0.1:8087;
            proxy_set_header Host ${DOLLAR}host;
            proxy_set_header X-Real-IP ${DOLLAR}remote_addr;
            proxy_set_header X-Forwarded-For ${DOLLAR}proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host ${DOLLAR}server_name;
            proxy_set_header X-Forwarded-Proto ${DOLLAR}scheme;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root  /usr/share/nginx/html;
        }
    }

    include /etc/nginx/conf.d/*.conf;
}
