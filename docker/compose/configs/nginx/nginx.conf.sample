user root;
# May be equal to `grep processor /proc/cpuinfo | wc -l`
worker_processes auto;
worker_cpu_affinity auto;

# PCRE JIT can speed up processing of regular expressions significantly.
pcre_jit on;

# error_log
error_log /var/log/nginx/error.log notice;

events {
    # Should be equal to `ulimit -n`
    worker_connections 1024;

    # Let each process accept multiple connections.
    multi_accept on;

    # Preferred connection method for newer linux versions.
    use epoll;
}

stream {
    server {
        listen 2222;
        proxy_pass gitlab-shell:2222;
    }
}
http {
    # Disables the “Server” response header
    server_tokens off;
    charset utf-8;

    # Sendfile copies data between one FD and other from within the kernel.
    # More efficient than read() + write(), since the requires transferring
    # data to and from the user space.
    sendfile on;

    # Tcp_nopush causes nginx to attempt to send its HTTP response head in one
    # packet, instead of using partial frames. This is useful for prepending
    # headers before calling sendfile, or for throughput optimization.
    tcp_nopush on;

    # Don't buffer data-sends (disable Nagle algorithm). Good for sending
    # frequent small bursts of data in real time.
    #
    tcp_nodelay on;

    # http://nginx.org/en/docs/hash.html
    types_hash_max_size 4096;
    include mime.types;
    default_type application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    # Logging Settings
    access_log /var/log/nginx/access.log main;

    # Gzip Settings
    gzip on;
    gzip_disable "msie6";

    gzip_comp_level 6;
    # gzip_comp_level 9;
    gzip_min_length 1100;
    gzip_buffers 16 8k;
    gzip_proxied any;
    # gzip_http_version 1.1;
    gzip_types text/plain application/xml text/css text/js text/xml application/x-javascript text/javascript application/json application/xml+rss;

    # Configure nginx to proxy large file requests

    # nginx does not buffer client requests and does not buffer proxy server requests
    proxy_request_buffering off;
    proxy_buffering off;

    # Temporary storage path, ensure this path has enough space
    client_body_temp_path /var/nginx/client_body_temp;
    # Maximum size of temporary files
    proxy_max_temp_file_size 150000M;

    # Maximum size of client requests
    client_max_body_size 0;

    # Increase timeout settings to support large file transfers
    client_body_timeout 300s;
    client_header_timeout 300s;
    send_timeout 300s;
    keepalive_timeout 300s;
    proxy_read_timeout 3600;
    proxy_connect_timeout 300;
    proxy_redirect off;
    proxy_http_version 1.1;

    map $http_upgrade $connection_upgrade {
      default upgrade;
      ''      close;
    }

    server {
        listen _SERVER_PORT;
        server_name _SERVER_DOMAIN;

        location / {
            proxy_pass http://csghub-portal:8090;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name:_SERVER_PORT;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $host;
        }

        location /api/ {
            proxy_pass http://csghub-server:8080/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location ~ ^/(hf|csg|ms)/ {
            proxy_pass http://csghub-server:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /temporal-ui/ {
            auth_basic "Please login with your account:";
            auth_basic_user_file /etc/nginx/ssl/.htpasswd;

            proxy_pass http://temporal-ui:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Proto $scheme;
       }

        location /endpoint/ {
            proxy_pass http://csghub-proxy:8083;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_cookie_flags ~ nosecure samesite=lax;
        }

        location ~* \.git(/.*)?$ {
            proxy_pass http://csghub-server:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto http;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root  /usr/share/nginx/html;
        }
    }

    server {
        listen 5000;
        server_name _SERVER_DOMAIN;

        client_max_body_size 0;
        chunked_transfer_encoding on;

        location /v2/ {
            if ($http_user_agent ~ "^(docker\/1\.(3|4|5(?!\.[0-9]-dev))|Go ).*$" ) {
                return 404;
            }

            proxy_pass                          http://registry:5000;
            proxy_set_header  Host              $http_host;
            proxy_set_header  X-Real-IP         $remote_addr;
            proxy_set_header  X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header  X-Forwarded-Proto $scheme;
            proxy_read_timeout                  900;
        }
    }

    server {
        listen 8000;
        server_name _SERVER_DOMAIN;

        location / {
            proxy_pass http://casdoor:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    server {
        listen 9000;
        server_name _SERVER_DOMAIN;

        ignore_invalid_headers off;
        client_max_body_size 0;

        location / {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 300;
            # Default is HTTP/1, keepalive is only enabled in HTTP/1.1
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;

            proxy_pass http://minio:9000;
       }
    }

    server {
        listen 9001;
        server_name _SERVER_DOMAIN;

        ignore_invalid_headers off;
        client_max_body_size 0;

        location / {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 300;
            # Default is HTTP/1, keepalive is only enabled in HTTP/1.1
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            chunked_transfer_encoding off;

            proxy_pass http://minio:9001;
       }
    }

    server {
        listen 80;
        server_name *._SPACE_APP_NAMESPACE._SPACE_APP_INTERNAL_DOMAIN;
        location / {
            proxy_http_version 1.1;
            proxy_pass http://_SPACE_APP_INTERNAL_HOST:_SPACE_APP_INTERNAL_PORT;
            proxy_set_header Host $host;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host $http_host;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
