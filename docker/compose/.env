####################################################################################
# CSGHUB Configuration
####################################################################################
## Version Configuration
CSGHUB_IMAGE_PREFIX="opencsg-registry.cn-beijing.cr.aliyuncs.com/opencsg_public"
CSGHUB_VERSION="v1.7.0-ee"

####################################################################################
# Nginx Configuration
####################################################################################
## Define the default access address, it can be IPv4 or Domain.
SERVER_DOMAIN="csghub.example.com"
## Define nginx listen port, blank as 80.
SERVER_PORT="80"
## Define nginx access protocol, http or https.
SERVER_PROTOCOL="http"
## Define nginx ssl certificates
# SERVER_SSL_CERT=""
# SERVER_SSL_KEY=""

####################################################################################
# Csghub-portal Configuration
####################################################################################
# If nginx enable https, please ensure it to `true`
CSGHUB_PORTAL_ENABLE_HTTPS=false
CSGHUB_MULTI_SYNC_ENABLED=true

####################################################################################
# Csghub-SERVER Configuration
####################################################################################
# If the machine performance is not good enable direct lfs upload
CSGHUB_LFS_DIRECT_UPLOAD=false

####################################################################################
# Data Volumes Configuration
####################################################################################
CSGHUB_DATA_DIR="./data"

####################################################################################
# GitLab-Shell Configuration
####################################################################################
GIT_SSH_PORT=2222

####################################################################################
# Space Application/Knative Serving Configuration
####################################################################################
## Using dedicate domain for space accessing
PUBLIC_ROOT_DOMAIN="public.example.com"
SPACE_APP_NAMESPACE="spaces"
## Define knative serving internal domain.
## It is knative network layer endpoint.
## it can be an internal lb or ip which will not be exposed to external
SPACE_APP_INTERNAL_DOMAIN="app.internal"
## Define kourier network plugin service ip and port.
SPACE_APP_INTERNAL_HOST="127.0.0.1"
## If ServiceType is LoadBalancer SPACE_APP_INTERNAL_PORT should be 80 or 443
SPACE_APP_INTERNAL_PORT="30541"

####################################################################################
# Kubernetes Cluster Configuration
####################################################################################
## If you want to use k8s cluster, you should set this to 1.
CSGHUB_WITH_K8S=0
## If using Space/Finetune/Inference/Model Evaluation/Dataflow functions and so on.
KUBE_CONFIG_DIR="/root/.kube"

####################################################################################
# Sensitive Content Checking
####################################################################################
# SENSITIVE_CONTENT_CHECK=1
## If using sensitive content detection service(SENSITIVE_CONTENT_CHECK=1),
##   fillup following server connection info.
# SENSITIVE_CHECK_ACCESS_KEY_ID=""
# SENSITIVE_CHECK_ACCESS_KEY_SECRET=""
# SENSITIVE_CHECK_REGION=""

####################################################################################
# Dataflow AI
####################################################################################
# AZURE_OPENAI_ENDPOINT=""
# AZURE_OPENAI_API_KEY=""
# OPENAI_API_VERSION=""
# AZURE_MODEL=""

####################################################################################
# Csghub-runner Configuration
####################################################################################
CSGHUB_PIP_INDEX_URL="https://pypi.tuna.tsinghua.edu.cn/simple"

####################################################################################
# (Advanced Configuration) (DO NOT UPDATE IF NOT NEEDED!!!)
####################################################################################
## You can using following configurations to configure using external resource.
## For example, you can use external postgres database.
## If you do not set these configurations, the script will use internal resources.
## If you set these configurations, the script will use external resources.
## Please ensure that the external resources are accessible and configured correctly.
####################################################################################
# Postgres Configuration
####################################################################################
## POSTGRES_ENABLED control whether to use external postgres database.
##  - POSTGRES_ENABLED ==> 0 or '', Use external postgres database.
##  - POSTGRES_ENABLED ==> 1, Use internal postgres database.
POSTGRES_ENABLED=1
## If using external postgres database, you need to set the following configurations:
##   And create databases csghub_server, csghub_portal, casdoor, temporal
## If using internal postgres database, you do not need to update any.
POSTGRES_USER="postgres"
POSTGRES_PASSWORD="Postgres@2025!"
POSTGRES_HOST="postgres"
POSTGRES_PORT="5432"
POSTGRES_TIMEZONE="Asia/Shanghai"

####################################################################################
# Registry Configuration (Advanced Configuration)
####################################################################################
## REGISTRY_ENABLED has the same effect as POSTGRES_ENABLED.
##  - REGISTRY_ENABLED ==> 0 or '', Use external docker registry.
##  - REGISTRY_ENABLED ==> 1, Use internal docker registry.
REGISTRY_ENABLED=1
## If using external docker registry, you need to set the following configurations:
## If using internal docker registry, you do not need to update any.
REGISTRY_PORT=5000
REGISTRY_ADDRESS="${SERVER_DOMAIN}:${REGISTRY_PORT}"
REGISTRY_NAMESPACE="csghub"
REGISTRY_USERNAME="registry"
REGISTRY_PASSWORD="Registry@2025!"

####################################################################################
# Kaniko Configuration (Advanced Configuration)
####################################################################################
KANIKO_ARGS="--skip-tls-verify,--insecure"

####################################################################################
# Minio Configuration
####################################################################################
## MINIO_ENABLED control whether to use external object storage.
##  - MINIO_ENABLED ==> 0 or '', Use external object storage.
##  - MINIO_ENABLED ==> 1, Use internal object storage(minio).
MINIO_ENABLED=1
MINIO_API_PORT=9000
MINIO_CONSOLE_PORT=9001
## If using external object storage, you need to set the following configurations:
##   And create buckets opencsg-server-lfs, opencsg-portal-storage, opencsg-registry-storage
## If using internal object storage, you do not need to update any.
## If using external object storage, set MINIO_ENDPOINT and MINIO_EXTERNAL_ENDPOINT with same value.
MINIO_ENDPOINT="${SERVER_DOMAIN}:${MINIO_API_PORT}"
## If not using Minio in internal network, just leave it blank.
MINIO_EXTERNAL_ENDPOINT=""
MINIO_ROOT_USER="minio"
MINIO_ROOT_PASSWORD="Minio@2025!"
MINIO_REGION="cn-north-1"
MINIO_ENABLE_SSL=false
USING_PATH_STYLE=true

####################################################################################
# Gitaly Configuration
####################################################################################
GITALY_ENABLED=1
GITALY_SERVER_SOCKET="tcp://gitaly:8075"
GITALY_STORAGE="default"
GITALY_AUTH_TOKEN="Gitaly@2025!"
GITALY_CLUSTER=false

####################################################################################
# Temporal Configuration
####################################################################################
## Define Temporal UI admin user
TEMPORAL_UI_ENABLED=1
TEMPORAL_CONSOLE_USER="temporal"
TEMPORAL_CONSOLE_PASSWORD="Temporal@2025!"

####################################################################################
# Casdoor Configuration
####################################################################################
## This cannot modify accessing port, Just restrive for configure
CASDOOR_PORT=8000

####################################################################################
# Nats Configuration
####################################################################################
NATS_ROOT_USER="natsadmin"
NATS_ROOT_PASSWORD="gALqqbP6SpftVdFzrU2URJ8k1G"

####################################################################################
# Fixed Configuration ( DO NOT UPDATE !!! )
####################################################################################
## Default csghub server token.
## - A 128-bit string consisting of numbers and lowercase letters.
HUB_SERVER_API_TOKEN="c7ab4948c36d6ecdf35fd4582def759ddd820f8899f5ff365ce16d7185cb2f609f3052e15681e931897259872391cbf46d78f4e75763a0a0633ef52abcdc840c"
STARHUB_JWT_SIGNING_KEY="e2kk6awudc3620ed9a"
