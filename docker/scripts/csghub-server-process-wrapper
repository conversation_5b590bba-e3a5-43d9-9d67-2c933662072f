#!/bin/bash

export PGHOST=${POSTGRES_HOST}
export PGPORT=${POSTGRES_PORT}
export PGUSER=${POSTGRES_SERVER_USER}
export PGPASSWORD=${POSTGRES_SERVER_PASS:-"$POSTGRES_SERVER_USER"}
export PGDATABASE=${POSTGRES_SERVER_DB:-"$POSTGRES_SERVER_USER"}

check_postgresql_isready() {
  until pg_isready -q -d $PGDATABASE; do
    sleep 2
  done
}

check_postgresql() {
  check_postgresql_isready

  echo "Waiting PostgreSQL ready..."
  until su - postgres -lc 'psql -t -A -c \\du' | grep -q "${POSTGRES_SERVER_USER}"; do
    sleep 2
  done
  echo "PostgreSQL is ready."
}

check_minio() {
  echo "Waiting MinIO ready..."
  until /usr/bin/curl -s http://${STARHUB_SERVER_S3_ENDPOINT}/minio/health/live; do
    sleep 2
  done
  echo "MinIO is ready."
}

check_postgresql

check_temporal() {
  echo "Waiting Temporal ready..."
    until /usr/bin/tctl cluster health; do
      sleep 2
    done
  echo "Temporal is ready."
}

if [ "$MINIO_ROOT_USER" == "minio" ]; then
  check_minio
fi

if [ "$TEMPORAL_ADDRESS" == "127.0.0.1:7233" ]; then
    check_temporal
fi

if [ "$STARHUB_SERVER_GITSERVER_TYPE" = "gitea" ]; then
  # The base64 encoded usename:password
  AUTH_HEADER=$(echo -n "$GITEA_USERNAME:$GITEA_PASSWORD" | base64)
  # Function to check if the Gitea service is ready
  check_gitea() {
    # Check the availability of the Gitea service
    # Replace the following command with the appropriate check for your service
    # For example, using curl to check if the Gitea API responds:
    curl -s -X GET --url $STARHUB_SERVER_GITSERVER_HOST/api/v1/version --header "Authorization: Basic $AUTH_HEADER" | grep "version"
  }

  # Wait for the database to be ready
  # echo "Waiting for the database to be ready..."
  # until telnet postgres 5432 </dev/null 2>&1 | grep -q "Connected"; do
  #     sleep 1
  # done
  # echo "Database is ready!"

  # Wait for the Gitea service to be ready
  echo "Waiting for Gitea service to be ready..."
  until check_gitea; do
    sleep 3
  done
  echo "Gitea service is ready!"
  echo "Running initialization commands..."


  # Delete if the access token named `webhook_access_token` already exist
  echo "Access token already exist, Delete it..."
  curl -s -X DELETE --url "$STARHUB_SERVER_GITSERVER_HOST/api/v1/users/$GITEA_USERNAME/tokens/webhook_access_token" --header "Authorization: Basic $AUTH_HEADER"

  echo "Creating access token..."
  # Create a new access token for $GITEA_USERNAME
  TOKEN_RESPONSE=$(curl -s -X POST \
    --url $STARHUB_SERVER_GITSERVER_HOST/api/v1/users/$GITEA_USERNAME/tokens \
    --data-urlencode "name=webhook_access_token" \
    --data-urlencode "scopes=read:user,write:user,write:admin,read:admin" \
    --header "accept: application/json" \
    --header "Content-Type: application/x-www-form-urlencoded" \
    --header "Authorization: Basic $AUTH_HEADER")

  # Extract access token from the response
  STARHUB_SERVER_GITSERVER_SECRET_KEY=$(echo "$TOKEN_RESPONSE" | jq -r '.sha1')

  # Get the system hook list
  webhooks=$(curl -s -X GET --url "$STARHUB_SERVER_GITSERVER_HOST/api/v1/admin/hooks" --header "Authorization: Basic $AUTH_HEADER")

  # Get the first hook type
  first_hook_type=$(echo "$webhooks" | jq -r '.[0].type')

  if [ -n "$first_hook_type" ] && [ "$first_hook_type" != "null" ]; then
    echo "System hook exists"
  else
    # Create a webhook to send push events
    curl -X POST \
      -H "Content-Type: application/json" \
      -d '{
            "type": "gitea",
            "authorization_header": "Bearer '"$STARHUB_SERVER_API_TOKEN"'",
            "config": {
                "is_system_webhook": "true",
                "url": "'"$STARHUB_SERVER_GITSERVER_WEBHOOK_URL"'",
                "content_type": "json",
                "insecure_ssl": "true"
            },
            "events": ["push"],
            "active": true
            }' \
      "$STARHUB_SERVER_GITSERVER_HOST/api/v1/admin/hooks?access_token=$STARHUB_SERVER_GITSERVER_SECRET_KEY"
  fi
fi


echo "Database setup..."
echo "Migration init"
/usr/bin/csghub-server migration init

echo "Migration migrate"
/usr/bin/csghub-server migration migrate

if [[ $(ls -A /etc/.kube/config* 2>dev/null) ]]; then
    echo "Copy kube configs."
    cp -a /etc/.kube /root
fi

echo "Reset temporary environments"
unset PGPASSWORD PGHOST PGPORT PGUSER PGDATABASE

if [ "$STARHUB_SERVER_SENSITIVE_CHECK_ENABLE" == "true" ]; then
  echo "Starting moderation..."
  supervisorctl start csghub-moderation
fi

echo "Starting server..."
exec /usr/bin/csghub-server start server
